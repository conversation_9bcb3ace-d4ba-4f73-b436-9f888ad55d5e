function sv(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const l of s.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function vd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var gd={exports:{}},Gs={},yd={exports:{}},V={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Eo=Symbol.for("react.element"),lv=Symbol.for("react.portal"),iv=Symbol.for("react.fragment"),av=Symbol.for("react.strict_mode"),uv=Symbol.for("react.profiler"),cv=Symbol.for("react.provider"),dv=Symbol.for("react.context"),fv=Symbol.for("react.forward_ref"),pv=Symbol.for("react.suspense"),mv=Symbol.for("react.memo"),hv=Symbol.for("react.lazy"),Pu=Symbol.iterator;function vv(e){return e===null||typeof e!="object"?null:(e=Pu&&e[Pu]||e["@@iterator"],typeof e=="function"?e:null)}var xd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},wd=Object.assign,Sd={};function Sr(e,t,n){this.props=e,this.context=t,this.refs=Sd,this.updater=n||xd}Sr.prototype.isReactComponent={};Sr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Sr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function jd(){}jd.prototype=Sr.prototype;function da(e,t,n){this.props=e,this.context=t,this.refs=Sd,this.updater=n||xd}var fa=da.prototype=new jd;fa.constructor=da;wd(fa,Sr.prototype);fa.isPureReactComponent=!0;var _u=Array.isArray,Ed=Object.prototype.hasOwnProperty,pa={current:null},Cd={key:!0,ref:!0,__self:!0,__source:!0};function Nd(e,t,n){var r,o={},s=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(s=""+t.key),t)Ed.call(t,r)&&!Cd.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:Eo,type:e,key:s,ref:l,props:o,_owner:pa.current}}function gv(e,t){return{$$typeof:Eo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ma(e){return typeof e=="object"&&e!==null&&e.$$typeof===Eo}function yv(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ru=/\/+/g;function Sl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?yv(""+e.key):t.toString(36)}function ss(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case Eo:case lv:l=!0}}if(l)return l=e,o=o(l),e=r===""?"."+Sl(l,0):r,_u(o)?(n="",e!=null&&(n=e.replace(Ru,"$&/")+"/"),ss(o,t,n,"",function(c){return c})):o!=null&&(ma(o)&&(o=gv(o,n+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(Ru,"$&/")+"/")+e)),t.push(o)),1;if(l=0,r=r===""?".":r+":",_u(e))for(var a=0;a<e.length;a++){s=e[a];var u=r+Sl(s,a);l+=ss(s,t,n,u,o)}else if(u=vv(e),typeof u=="function")for(e=u.call(e),a=0;!(s=e.next()).done;)s=s.value,u=r+Sl(s,a++),l+=ss(s,t,n,u,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function Oo(e,t,n){if(e==null)return e;var r=[],o=0;return ss(e,r,"","",function(s){return t.call(n,s,o++)}),r}function xv(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Fe={current:null},ls={transition:null},wv={ReactCurrentDispatcher:Fe,ReactCurrentBatchConfig:ls,ReactCurrentOwner:pa};function kd(){throw Error("act(...) is not supported in production builds of React.")}V.Children={map:Oo,forEach:function(e,t,n){Oo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Oo(e,function(){t++}),t},toArray:function(e){return Oo(e,function(t){return t})||[]},only:function(e){if(!ma(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};V.Component=Sr;V.Fragment=iv;V.Profiler=uv;V.PureComponent=da;V.StrictMode=av;V.Suspense=pv;V.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=wv;V.act=kd;V.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=wd({},e.props),o=e.key,s=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,l=pa.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)Ed.call(t,u)&&!Cd.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:Eo,type:e.type,key:o,ref:s,props:r,_owner:l}};V.createContext=function(e){return e={$$typeof:dv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:cv,_context:e},e.Consumer=e};V.createElement=Nd;V.createFactory=function(e){var t=Nd.bind(null,e);return t.type=e,t};V.createRef=function(){return{current:null}};V.forwardRef=function(e){return{$$typeof:fv,render:e}};V.isValidElement=ma;V.lazy=function(e){return{$$typeof:hv,_payload:{_status:-1,_result:e},_init:xv}};V.memo=function(e,t){return{$$typeof:mv,type:e,compare:t===void 0?null:t}};V.startTransition=function(e){var t=ls.transition;ls.transition={};try{e()}finally{ls.transition=t}};V.unstable_act=kd;V.useCallback=function(e,t){return Fe.current.useCallback(e,t)};V.useContext=function(e){return Fe.current.useContext(e)};V.useDebugValue=function(){};V.useDeferredValue=function(e){return Fe.current.useDeferredValue(e)};V.useEffect=function(e,t){return Fe.current.useEffect(e,t)};V.useId=function(){return Fe.current.useId()};V.useImperativeHandle=function(e,t,n){return Fe.current.useImperativeHandle(e,t,n)};V.useInsertionEffect=function(e,t){return Fe.current.useInsertionEffect(e,t)};V.useLayoutEffect=function(e,t){return Fe.current.useLayoutEffect(e,t)};V.useMemo=function(e,t){return Fe.current.useMemo(e,t)};V.useReducer=function(e,t,n){return Fe.current.useReducer(e,t,n)};V.useRef=function(e){return Fe.current.useRef(e)};V.useState=function(e){return Fe.current.useState(e)};V.useSyncExternalStore=function(e,t,n){return Fe.current.useSyncExternalStore(e,t,n)};V.useTransition=function(){return Fe.current.useTransition()};V.version="18.3.1";yd.exports=V;var d=yd.exports;const _t=vd(d),ha=sv({__proto__:null,default:_t},[d]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sv=d,jv=Symbol.for("react.element"),Ev=Symbol.for("react.fragment"),Cv=Object.prototype.hasOwnProperty,Nv=Sv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,kv={key:!0,ref:!0,__self:!0,__source:!0};function Td(e,t,n){var r,o={},s=null,l=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)Cv.call(t,r)&&!kv.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:jv,type:e,key:s,ref:l,props:o,_owner:Nv.current}}Gs.Fragment=Ev;Gs.jsx=Td;Gs.jsxs=Td;gd.exports=Gs;var i=gd.exports,Pd={exports:{}},Je={},_d={exports:{}},Rd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(b,D){var N=b.length;b.push(D);e:for(;0<N;){var M=N-1>>>1,U=b[M];if(0<o(U,D))b[M]=D,b[N]=U,N=M;else break e}}function n(b){return b.length===0?null:b[0]}function r(b){if(b.length===0)return null;var D=b[0],N=b.pop();if(N!==D){b[0]=N;e:for(var M=0,U=b.length,Ee=U>>>1;M<Ee;){var tt=2*(M+1)-1,de=b[tt],Ge=tt+1,hn=b[Ge];if(0>o(de,N))Ge<U&&0>o(hn,de)?(b[M]=hn,b[Ge]=N,M=Ge):(b[M]=de,b[tt]=N,M=tt);else if(Ge<U&&0>o(hn,N))b[M]=hn,b[Ge]=N,M=Ge;else break e}}return D}function o(b,D){var N=b.sortIndex-D.sortIndex;return N!==0?N:b.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var l=Date,a=l.now();e.unstable_now=function(){return l.now()-a}}var u=[],c=[],m=1,p=null,g=3,w=!1,j=!1,y=!1,x=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,f=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(b){for(var D=n(c);D!==null;){if(D.callback===null)r(c);else if(D.startTime<=b)r(c),D.sortIndex=D.expirationTime,t(u,D);else break;D=n(c)}}function S(b){if(y=!1,v(b),!j)if(n(u)!==null)j=!0,H(E);else{var D=n(c);D!==null&&je(S,D.startTime-b)}}function E(b,D){j=!1,y&&(y=!1,h(_),_=-1),w=!0;var N=g;try{for(v(D),p=n(u);p!==null&&(!(p.expirationTime>D)||b&&!Q());){var M=p.callback;if(typeof M=="function"){p.callback=null,g=p.priorityLevel;var U=M(p.expirationTime<=D);D=e.unstable_now(),typeof U=="function"?p.callback=U:p===n(u)&&r(u),v(D)}else r(u);p=n(u)}if(p!==null)var Ee=!0;else{var tt=n(c);tt!==null&&je(S,tt.startTime-D),Ee=!1}return Ee}finally{p=null,g=N,w=!1}}var T=!1,C=null,_=-1,z=5,O=-1;function Q(){return!(e.unstable_now()-O<z)}function F(){if(C!==null){var b=e.unstable_now();O=b;var D=!0;try{D=C(!0,b)}finally{D?q():(T=!1,C=null)}}else T=!1}var q;if(typeof f=="function")q=function(){f(F)};else if(typeof MessageChannel<"u"){var A=new MessageChannel,te=A.port2;A.port1.onmessage=F,q=function(){te.postMessage(null)}}else q=function(){x(F,0)};function H(b){C=b,T||(T=!0,q())}function je(b,D){_=x(function(){b(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(b){b.callback=null},e.unstable_continueExecution=function(){j||w||(j=!0,H(E))},e.unstable_forceFrameRate=function(b){0>b||125<b?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<b?Math.floor(1e3/b):5},e.unstable_getCurrentPriorityLevel=function(){return g},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(b){switch(g){case 1:case 2:case 3:var D=3;break;default:D=g}var N=g;g=D;try{return b()}finally{g=N}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(b,D){switch(b){case 1:case 2:case 3:case 4:case 5:break;default:b=3}var N=g;g=b;try{return D()}finally{g=N}},e.unstable_scheduleCallback=function(b,D,N){var M=e.unstable_now();switch(typeof N=="object"&&N!==null?(N=N.delay,N=typeof N=="number"&&0<N?M+N:M):N=M,b){case 1:var U=-1;break;case 2:U=250;break;case 5:U=**********;break;case 4:U=1e4;break;default:U=5e3}return U=N+U,b={id:m++,callback:D,priorityLevel:b,startTime:N,expirationTime:U,sortIndex:-1},N>M?(b.sortIndex=N,t(c,b),n(u)===null&&b===n(c)&&(y?(h(_),_=-1):y=!0,je(S,N-M))):(b.sortIndex=U,t(u,b),j||w||(j=!0,H(E))),b},e.unstable_shouldYield=Q,e.unstable_wrapCallback=function(b){var D=g;return function(){var N=g;g=D;try{return b.apply(this,arguments)}finally{g=N}}}})(Rd);_d.exports=Rd;var Tv=_d.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pv=d,Ze=Tv;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var bd=new Set,to={};function Ln(e,t){pr(e,t),pr(e+"Capture",t)}function pr(e,t){for(to[e]=t,e=0;e<t.length;e++)bd.add(t[e])}var Ot=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ai=Object.prototype.hasOwnProperty,_v=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,bu={},Iu={};function Rv(e){return ai.call(Iu,e)?!0:ai.call(bu,e)?!1:_v.test(e)?Iu[e]=!0:(bu[e]=!0,!1)}function bv(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Iv(e,t,n,r){if(t===null||typeof t>"u"||bv(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function $e(e,t,n,r,o,s,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=l}var ke={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ke[e]=new $e(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ke[t]=new $e(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ke[e]=new $e(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ke[e]=new $e(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ke[e]=new $e(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ke[e]=new $e(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ke[e]=new $e(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ke[e]=new $e(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ke[e]=new $e(e,5,!1,e.toLowerCase(),null,!1,!1)});var va=/[\-:]([a-z])/g;function ga(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(va,ga);ke[t]=new $e(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(va,ga);ke[t]=new $e(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(va,ga);ke[t]=new $e(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ke[e]=new $e(e,1,!1,e.toLowerCase(),null,!1,!1)});ke.xlinkHref=new $e("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ke[e]=new $e(e,1,!1,e.toLowerCase(),null,!0,!0)});function ya(e,t,n,r){var o=ke.hasOwnProperty(t)?ke[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Iv(t,n,o,r)&&(n=null),r||o===null?Rv(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var $t=Pv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Mo=Symbol.for("react.element"),Un=Symbol.for("react.portal"),Bn=Symbol.for("react.fragment"),xa=Symbol.for("react.strict_mode"),ui=Symbol.for("react.profiler"),Id=Symbol.for("react.provider"),Ld=Symbol.for("react.context"),wa=Symbol.for("react.forward_ref"),ci=Symbol.for("react.suspense"),di=Symbol.for("react.suspense_list"),Sa=Symbol.for("react.memo"),Qt=Symbol.for("react.lazy"),zd=Symbol.for("react.offscreen"),Lu=Symbol.iterator;function Rr(e){return e===null||typeof e!="object"?null:(e=Lu&&e[Lu]||e["@@iterator"],typeof e=="function"?e:null)}var ce=Object.assign,jl;function $r(e){if(jl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);jl=t&&t[1]||""}return`
`+jl+e}var El=!1;function Cl(e,t){if(!e||El)return"";El=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var o=c.stack.split(`
`),s=r.stack.split(`
`),l=o.length-1,a=s.length-1;1<=l&&0<=a&&o[l]!==s[a];)a--;for(;1<=l&&0<=a;l--,a--)if(o[l]!==s[a]){if(l!==1||a!==1)do if(l--,a--,0>a||o[l]!==s[a]){var u=`
`+o[l].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=l&&0<=a);break}}}finally{El=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?$r(e):""}function Lv(e){switch(e.tag){case 5:return $r(e.type);case 16:return $r("Lazy");case 13:return $r("Suspense");case 19:return $r("SuspenseList");case 0:case 2:case 15:return e=Cl(e.type,!1),e;case 11:return e=Cl(e.type.render,!1),e;case 1:return e=Cl(e.type,!0),e;default:return""}}function fi(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Bn:return"Fragment";case Un:return"Portal";case ui:return"Profiler";case xa:return"StrictMode";case ci:return"Suspense";case di:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ld:return(e.displayName||"Context")+".Consumer";case Id:return(e._context.displayName||"Context")+".Provider";case wa:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Sa:return t=e.displayName||null,t!==null?t:fi(e.type)||"Memo";case Qt:t=e._payload,e=e._init;try{return fi(e(t))}catch{}}return null}function zv(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return fi(t);case 8:return t===xa?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function an(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Dd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Dv(e){var t=Dd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(l){r=""+l,s.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ao(e){e._valueTracker||(e._valueTracker=Dv(e))}function Od(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Dd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ss(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function pi(e,t){var n=t.checked;return ce({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function zu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=an(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Md(e,t){t=t.checked,t!=null&&ya(e,"checked",t,!1)}function mi(e,t){Md(e,t);var n=an(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?hi(e,t.type,n):t.hasOwnProperty("defaultValue")&&hi(e,t.type,an(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Du(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function hi(e,t,n){(t!=="number"||Ss(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Ur=Array.isArray;function tr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+an(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function vi(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return ce({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ou(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(P(92));if(Ur(n)){if(1<n.length)throw Error(P(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:an(n)}}function Ad(e,t){var n=an(t.value),r=an(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Mu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Fd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function gi(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Fd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Fo,$d=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Fo=Fo||document.createElement("div"),Fo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Fo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function no(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Wr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ov=["Webkit","ms","Moz","O"];Object.keys(Wr).forEach(function(e){Ov.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Wr[t]=Wr[e]})});function Ud(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Wr.hasOwnProperty(e)&&Wr[e]?(""+t).trim():t+"px"}function Bd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Ud(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Mv=ce({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function yi(e,t){if(t){if(Mv[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function xi(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var wi=null;function ja(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Si=null,nr=null,rr=null;function Au(e){if(e=ko(e)){if(typeof Si!="function")throw Error(P(280));var t=e.stateNode;t&&(t=Js(t),Si(e.stateNode,e.type,t))}}function Vd(e){nr?rr?rr.push(e):rr=[e]:nr=e}function Wd(){if(nr){var e=nr,t=rr;if(rr=nr=null,Au(e),t)for(e=0;e<t.length;e++)Au(t[e])}}function Qd(e,t){return e(t)}function Hd(){}var Nl=!1;function Kd(e,t,n){if(Nl)return e(t,n);Nl=!0;try{return Qd(e,t,n)}finally{Nl=!1,(nr!==null||rr!==null)&&(Hd(),Wd())}}function ro(e,t){var n=e.stateNode;if(n===null)return null;var r=Js(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(P(231,t,typeof n));return n}var ji=!1;if(Ot)try{var br={};Object.defineProperty(br,"passive",{get:function(){ji=!0}}),window.addEventListener("test",br,br),window.removeEventListener("test",br,br)}catch{ji=!1}function Av(e,t,n,r,o,s,l,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(m){this.onError(m)}}var Qr=!1,js=null,Es=!1,Ei=null,Fv={onError:function(e){Qr=!0,js=e}};function $v(e,t,n,r,o,s,l,a,u){Qr=!1,js=null,Av.apply(Fv,arguments)}function Uv(e,t,n,r,o,s,l,a,u){if($v.apply(this,arguments),Qr){if(Qr){var c=js;Qr=!1,js=null}else throw Error(P(198));Es||(Es=!0,Ei=c)}}function zn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Gd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Fu(e){if(zn(e)!==e)throw Error(P(188))}function Bv(e){var t=e.alternate;if(!t){if(t=zn(e),t===null)throw Error(P(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return Fu(o),e;if(s===r)return Fu(o),t;s=s.sibling}throw Error(P(188))}if(n.return!==r.return)n=o,r=s;else{for(var l=!1,a=o.child;a;){if(a===n){l=!0,n=o,r=s;break}if(a===r){l=!0,r=o,n=s;break}a=a.sibling}if(!l){for(a=s.child;a;){if(a===n){l=!0,n=s,r=o;break}if(a===r){l=!0,r=s,n=o;break}a=a.sibling}if(!l)throw Error(P(189))}}if(n.alternate!==r)throw Error(P(190))}if(n.tag!==3)throw Error(P(188));return n.stateNode.current===n?e:t}function Xd(e){return e=Bv(e),e!==null?Yd(e):null}function Yd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Yd(e);if(t!==null)return t;e=e.sibling}return null}var qd=Ze.unstable_scheduleCallback,$u=Ze.unstable_cancelCallback,Vv=Ze.unstable_shouldYield,Wv=Ze.unstable_requestPaint,pe=Ze.unstable_now,Qv=Ze.unstable_getCurrentPriorityLevel,Ea=Ze.unstable_ImmediatePriority,Zd=Ze.unstable_UserBlockingPriority,Cs=Ze.unstable_NormalPriority,Hv=Ze.unstable_LowPriority,Jd=Ze.unstable_IdlePriority,Xs=null,Nt=null;function Kv(e){if(Nt&&typeof Nt.onCommitFiberRoot=="function")try{Nt.onCommitFiberRoot(Xs,e,void 0,(e.current.flags&128)===128)}catch{}}var gt=Math.clz32?Math.clz32:Yv,Gv=Math.log,Xv=Math.LN2;function Yv(e){return e>>>=0,e===0?32:31-(Gv(e)/Xv|0)|0}var $o=64,Uo=4194304;function Br(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ns(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,l=n&268435455;if(l!==0){var a=l&~o;a!==0?r=Br(a):(s&=l,s!==0&&(r=Br(s)))}else l=n&~o,l!==0?r=Br(l):s!==0&&(r=Br(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-gt(t),o=1<<n,r|=e[n],t&=~o;return r}function qv(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Zv(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var l=31-gt(s),a=1<<l,u=o[l];u===-1?(!(a&n)||a&r)&&(o[l]=qv(a,t)):u<=t&&(e.expiredLanes|=a),s&=~a}}function Ci(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ef(){var e=$o;return $o<<=1,!($o&4194240)&&($o=64),e}function kl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Co(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-gt(t),e[t]=n}function Jv(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-gt(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function Ca(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-gt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var Y=0;function tf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var nf,Na,rf,of,sf,Ni=!1,Bo=[],Zt=null,Jt=null,en=null,oo=new Map,so=new Map,Kt=[],eg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Uu(e,t){switch(e){case"focusin":case"focusout":Zt=null;break;case"dragenter":case"dragleave":Jt=null;break;case"mouseover":case"mouseout":en=null;break;case"pointerover":case"pointerout":oo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":so.delete(t.pointerId)}}function Ir(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=ko(t),t!==null&&Na(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function tg(e,t,n,r,o){switch(t){case"focusin":return Zt=Ir(Zt,e,t,n,r,o),!0;case"dragenter":return Jt=Ir(Jt,e,t,n,r,o),!0;case"mouseover":return en=Ir(en,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return oo.set(s,Ir(oo.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,so.set(s,Ir(so.get(s)||null,e,t,n,r,o)),!0}return!1}function lf(e){var t=wn(e.target);if(t!==null){var n=zn(t);if(n!==null){if(t=n.tag,t===13){if(t=Gd(n),t!==null){e.blockedOn=t,sf(e.priority,function(){rf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function is(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ki(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);wi=r,n.target.dispatchEvent(r),wi=null}else return t=ko(n),t!==null&&Na(t),e.blockedOn=n,!1;t.shift()}return!0}function Bu(e,t,n){is(e)&&n.delete(t)}function ng(){Ni=!1,Zt!==null&&is(Zt)&&(Zt=null),Jt!==null&&is(Jt)&&(Jt=null),en!==null&&is(en)&&(en=null),oo.forEach(Bu),so.forEach(Bu)}function Lr(e,t){e.blockedOn===t&&(e.blockedOn=null,Ni||(Ni=!0,Ze.unstable_scheduleCallback(Ze.unstable_NormalPriority,ng)))}function lo(e){function t(o){return Lr(o,e)}if(0<Bo.length){Lr(Bo[0],e);for(var n=1;n<Bo.length;n++){var r=Bo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Zt!==null&&Lr(Zt,e),Jt!==null&&Lr(Jt,e),en!==null&&Lr(en,e),oo.forEach(t),so.forEach(t),n=0;n<Kt.length;n++)r=Kt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Kt.length&&(n=Kt[0],n.blockedOn===null);)lf(n),n.blockedOn===null&&Kt.shift()}var or=$t.ReactCurrentBatchConfig,ks=!0;function rg(e,t,n,r){var o=Y,s=or.transition;or.transition=null;try{Y=1,ka(e,t,n,r)}finally{Y=o,or.transition=s}}function og(e,t,n,r){var o=Y,s=or.transition;or.transition=null;try{Y=4,ka(e,t,n,r)}finally{Y=o,or.transition=s}}function ka(e,t,n,r){if(ks){var o=ki(e,t,n,r);if(o===null)Ol(e,t,r,Ts,n),Uu(e,r);else if(tg(o,e,t,n,r))r.stopPropagation();else if(Uu(e,r),t&4&&-1<eg.indexOf(e)){for(;o!==null;){var s=ko(o);if(s!==null&&nf(s),s=ki(e,t,n,r),s===null&&Ol(e,t,r,Ts,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else Ol(e,t,r,null,n)}}var Ts=null;function ki(e,t,n,r){if(Ts=null,e=ja(r),e=wn(e),e!==null)if(t=zn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Gd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ts=e,null}function af(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Qv()){case Ea:return 1;case Zd:return 4;case Cs:case Hv:return 16;case Jd:return 536870912;default:return 16}default:return 16}}var Xt=null,Ta=null,as=null;function uf(){if(as)return as;var e,t=Ta,n=t.length,r,o="value"in Xt?Xt.value:Xt.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===o[s-r];r++);return as=o.slice(e,1<r?1-r:void 0)}function us(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Vo(){return!0}function Vu(){return!1}function et(e){function t(n,r,o,s,l){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=l,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Vo:Vu,this.isPropagationStopped=Vu,this}return ce(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Vo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Vo)},persist:function(){},isPersistent:Vo}),t}var jr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Pa=et(jr),No=ce({},jr,{view:0,detail:0}),sg=et(No),Tl,Pl,zr,Ys=ce({},No,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_a,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==zr&&(zr&&e.type==="mousemove"?(Tl=e.screenX-zr.screenX,Pl=e.screenY-zr.screenY):Pl=Tl=0,zr=e),Tl)},movementY:function(e){return"movementY"in e?e.movementY:Pl}}),Wu=et(Ys),lg=ce({},Ys,{dataTransfer:0}),ig=et(lg),ag=ce({},No,{relatedTarget:0}),_l=et(ag),ug=ce({},jr,{animationName:0,elapsedTime:0,pseudoElement:0}),cg=et(ug),dg=ce({},jr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),fg=et(dg),pg=ce({},jr,{data:0}),Qu=et(pg),mg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},hg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},vg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function gg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=vg[e])?!!t[e]:!1}function _a(){return gg}var yg=ce({},No,{key:function(e){if(e.key){var t=mg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=us(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?hg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_a,charCode:function(e){return e.type==="keypress"?us(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?us(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),xg=et(yg),wg=ce({},Ys,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Hu=et(wg),Sg=ce({},No,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_a}),jg=et(Sg),Eg=ce({},jr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Cg=et(Eg),Ng=ce({},Ys,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),kg=et(Ng),Tg=[9,13,27,32],Ra=Ot&&"CompositionEvent"in window,Hr=null;Ot&&"documentMode"in document&&(Hr=document.documentMode);var Pg=Ot&&"TextEvent"in window&&!Hr,cf=Ot&&(!Ra||Hr&&8<Hr&&11>=Hr),Ku=" ",Gu=!1;function df(e,t){switch(e){case"keyup":return Tg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ff(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Vn=!1;function _g(e,t){switch(e){case"compositionend":return ff(t);case"keypress":return t.which!==32?null:(Gu=!0,Ku);case"textInput":return e=t.data,e===Ku&&Gu?null:e;default:return null}}function Rg(e,t){if(Vn)return e==="compositionend"||!Ra&&df(e,t)?(e=uf(),as=Ta=Xt=null,Vn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return cf&&t.locale!=="ko"?null:t.data;default:return null}}var bg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Xu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!bg[e.type]:t==="textarea"}function pf(e,t,n,r){Vd(r),t=Ps(t,"onChange"),0<t.length&&(n=new Pa("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Kr=null,io=null;function Ig(e){Cf(e,0)}function qs(e){var t=Hn(e);if(Od(t))return e}function Lg(e,t){if(e==="change")return t}var mf=!1;if(Ot){var Rl;if(Ot){var bl="oninput"in document;if(!bl){var Yu=document.createElement("div");Yu.setAttribute("oninput","return;"),bl=typeof Yu.oninput=="function"}Rl=bl}else Rl=!1;mf=Rl&&(!document.documentMode||9<document.documentMode)}function qu(){Kr&&(Kr.detachEvent("onpropertychange",hf),io=Kr=null)}function hf(e){if(e.propertyName==="value"&&qs(io)){var t=[];pf(t,io,e,ja(e)),Kd(Ig,t)}}function zg(e,t,n){e==="focusin"?(qu(),Kr=t,io=n,Kr.attachEvent("onpropertychange",hf)):e==="focusout"&&qu()}function Dg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return qs(io)}function Og(e,t){if(e==="click")return qs(t)}function Mg(e,t){if(e==="input"||e==="change")return qs(t)}function Ag(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var xt=typeof Object.is=="function"?Object.is:Ag;function ao(e,t){if(xt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ai.call(t,o)||!xt(e[o],t[o]))return!1}return!0}function Zu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ju(e,t){var n=Zu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Zu(n)}}function vf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?vf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function gf(){for(var e=window,t=Ss();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ss(e.document)}return t}function ba(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Fg(e){var t=gf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&vf(n.ownerDocument.documentElement,n)){if(r!==null&&ba(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=Ju(n,s);var l=Ju(n,r);o&&l&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var $g=Ot&&"documentMode"in document&&11>=document.documentMode,Wn=null,Ti=null,Gr=null,Pi=!1;function ec(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Pi||Wn==null||Wn!==Ss(r)||(r=Wn,"selectionStart"in r&&ba(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Gr&&ao(Gr,r)||(Gr=r,r=Ps(Ti,"onSelect"),0<r.length&&(t=new Pa("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Wn)))}function Wo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Qn={animationend:Wo("Animation","AnimationEnd"),animationiteration:Wo("Animation","AnimationIteration"),animationstart:Wo("Animation","AnimationStart"),transitionend:Wo("Transition","TransitionEnd")},Il={},yf={};Ot&&(yf=document.createElement("div").style,"AnimationEvent"in window||(delete Qn.animationend.animation,delete Qn.animationiteration.animation,delete Qn.animationstart.animation),"TransitionEvent"in window||delete Qn.transitionend.transition);function Zs(e){if(Il[e])return Il[e];if(!Qn[e])return e;var t=Qn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in yf)return Il[e]=t[n];return e}var xf=Zs("animationend"),wf=Zs("animationiteration"),Sf=Zs("animationstart"),jf=Zs("transitionend"),Ef=new Map,tc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function cn(e,t){Ef.set(e,t),Ln(t,[e])}for(var Ll=0;Ll<tc.length;Ll++){var zl=tc[Ll],Ug=zl.toLowerCase(),Bg=zl[0].toUpperCase()+zl.slice(1);cn(Ug,"on"+Bg)}cn(xf,"onAnimationEnd");cn(wf,"onAnimationIteration");cn(Sf,"onAnimationStart");cn("dblclick","onDoubleClick");cn("focusin","onFocus");cn("focusout","onBlur");cn(jf,"onTransitionEnd");pr("onMouseEnter",["mouseout","mouseover"]);pr("onMouseLeave",["mouseout","mouseover"]);pr("onPointerEnter",["pointerout","pointerover"]);pr("onPointerLeave",["pointerout","pointerover"]);Ln("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ln("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ln("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ln("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ln("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ln("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Vr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Vg=new Set("cancel close invalid load scroll toggle".split(" ").concat(Vr));function nc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Uv(r,t,void 0,e),e.currentTarget=null}function Cf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var l=r.length-1;0<=l;l--){var a=r[l],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==s&&o.isPropagationStopped())break e;nc(o,a,c),s=u}else for(l=0;l<r.length;l++){if(a=r[l],u=a.instance,c=a.currentTarget,a=a.listener,u!==s&&o.isPropagationStopped())break e;nc(o,a,c),s=u}}}if(Es)throw e=Ei,Es=!1,Ei=null,e}function re(e,t){var n=t[Li];n===void 0&&(n=t[Li]=new Set);var r=e+"__bubble";n.has(r)||(Nf(t,e,2,!1),n.add(r))}function Dl(e,t,n){var r=0;t&&(r|=4),Nf(n,e,r,t)}var Qo="_reactListening"+Math.random().toString(36).slice(2);function uo(e){if(!e[Qo]){e[Qo]=!0,bd.forEach(function(n){n!=="selectionchange"&&(Vg.has(n)||Dl(n,!1,e),Dl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Qo]||(t[Qo]=!0,Dl("selectionchange",!1,t))}}function Nf(e,t,n,r){switch(af(t)){case 1:var o=rg;break;case 4:o=og;break;default:o=ka}n=o.bind(null,t,n,e),o=void 0,!ji||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ol(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(l===4)for(l=r.return;l!==null;){var u=l.tag;if((u===3||u===4)&&(u=l.stateNode.containerInfo,u===o||u.nodeType===8&&u.parentNode===o))return;l=l.return}for(;a!==null;){if(l=wn(a),l===null)return;if(u=l.tag,u===5||u===6){r=s=l;continue e}a=a.parentNode}}r=r.return}Kd(function(){var c=s,m=ja(n),p=[];e:{var g=Ef.get(e);if(g!==void 0){var w=Pa,j=e;switch(e){case"keypress":if(us(n)===0)break e;case"keydown":case"keyup":w=xg;break;case"focusin":j="focus",w=_l;break;case"focusout":j="blur",w=_l;break;case"beforeblur":case"afterblur":w=_l;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Wu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=ig;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=jg;break;case xf:case wf:case Sf:w=cg;break;case jf:w=Cg;break;case"scroll":w=sg;break;case"wheel":w=kg;break;case"copy":case"cut":case"paste":w=fg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Hu}var y=(t&4)!==0,x=!y&&e==="scroll",h=y?g!==null?g+"Capture":null:g;y=[];for(var f=c,v;f!==null;){v=f;var S=v.stateNode;if(v.tag===5&&S!==null&&(v=S,h!==null&&(S=ro(f,h),S!=null&&y.push(co(f,S,v)))),x)break;f=f.return}0<y.length&&(g=new w(g,j,null,n,m),p.push({event:g,listeners:y}))}}if(!(t&7)){e:{if(g=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",g&&n!==wi&&(j=n.relatedTarget||n.fromElement)&&(wn(j)||j[Mt]))break e;if((w||g)&&(g=m.window===m?m:(g=m.ownerDocument)?g.defaultView||g.parentWindow:window,w?(j=n.relatedTarget||n.toElement,w=c,j=j?wn(j):null,j!==null&&(x=zn(j),j!==x||j.tag!==5&&j.tag!==6)&&(j=null)):(w=null,j=c),w!==j)){if(y=Wu,S="onMouseLeave",h="onMouseEnter",f="mouse",(e==="pointerout"||e==="pointerover")&&(y=Hu,S="onPointerLeave",h="onPointerEnter",f="pointer"),x=w==null?g:Hn(w),v=j==null?g:Hn(j),g=new y(S,f+"leave",w,n,m),g.target=x,g.relatedTarget=v,S=null,wn(m)===c&&(y=new y(h,f+"enter",j,n,m),y.target=v,y.relatedTarget=x,S=y),x=S,w&&j)t:{for(y=w,h=j,f=0,v=y;v;v=Mn(v))f++;for(v=0,S=h;S;S=Mn(S))v++;for(;0<f-v;)y=Mn(y),f--;for(;0<v-f;)h=Mn(h),v--;for(;f--;){if(y===h||h!==null&&y===h.alternate)break t;y=Mn(y),h=Mn(h)}y=null}else y=null;w!==null&&rc(p,g,w,y,!1),j!==null&&x!==null&&rc(p,x,j,y,!0)}}e:{if(g=c?Hn(c):window,w=g.nodeName&&g.nodeName.toLowerCase(),w==="select"||w==="input"&&g.type==="file")var E=Lg;else if(Xu(g))if(mf)E=Mg;else{E=Dg;var T=zg}else(w=g.nodeName)&&w.toLowerCase()==="input"&&(g.type==="checkbox"||g.type==="radio")&&(E=Og);if(E&&(E=E(e,c))){pf(p,E,n,m);break e}T&&T(e,g,c),e==="focusout"&&(T=g._wrapperState)&&T.controlled&&g.type==="number"&&hi(g,"number",g.value)}switch(T=c?Hn(c):window,e){case"focusin":(Xu(T)||T.contentEditable==="true")&&(Wn=T,Ti=c,Gr=null);break;case"focusout":Gr=Ti=Wn=null;break;case"mousedown":Pi=!0;break;case"contextmenu":case"mouseup":case"dragend":Pi=!1,ec(p,n,m);break;case"selectionchange":if($g)break;case"keydown":case"keyup":ec(p,n,m)}var C;if(Ra)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else Vn?df(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(cf&&n.locale!=="ko"&&(Vn||_!=="onCompositionStart"?_==="onCompositionEnd"&&Vn&&(C=uf()):(Xt=m,Ta="value"in Xt?Xt.value:Xt.textContent,Vn=!0)),T=Ps(c,_),0<T.length&&(_=new Qu(_,e,null,n,m),p.push({event:_,listeners:T}),C?_.data=C:(C=ff(n),C!==null&&(_.data=C)))),(C=Pg?_g(e,n):Rg(e,n))&&(c=Ps(c,"onBeforeInput"),0<c.length&&(m=new Qu("onBeforeInput","beforeinput",null,n,m),p.push({event:m,listeners:c}),m.data=C))}Cf(p,t)})}function co(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ps(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=ro(e,n),s!=null&&r.unshift(co(e,s,o)),s=ro(e,t),s!=null&&r.push(co(e,s,o))),e=e.return}return r}function Mn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function rc(e,t,n,r,o){for(var s=t._reactName,l=[];n!==null&&n!==r;){var a=n,u=a.alternate,c=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&c!==null&&(a=c,o?(u=ro(n,s),u!=null&&l.unshift(co(n,u,a))):o||(u=ro(n,s),u!=null&&l.push(co(n,u,a)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var Wg=/\r\n?/g,Qg=/\u0000|\uFFFD/g;function oc(e){return(typeof e=="string"?e:""+e).replace(Wg,`
`).replace(Qg,"")}function Ho(e,t,n){if(t=oc(t),oc(e)!==t&&n)throw Error(P(425))}function _s(){}var _i=null,Ri=null;function bi(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ii=typeof setTimeout=="function"?setTimeout:void 0,Hg=typeof clearTimeout=="function"?clearTimeout:void 0,sc=typeof Promise=="function"?Promise:void 0,Kg=typeof queueMicrotask=="function"?queueMicrotask:typeof sc<"u"?function(e){return sc.resolve(null).then(e).catch(Gg)}:Ii;function Gg(e){setTimeout(function(){throw e})}function Ml(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),lo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);lo(t)}function tn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function lc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Er=Math.random().toString(36).slice(2),Et="__reactFiber$"+Er,fo="__reactProps$"+Er,Mt="__reactContainer$"+Er,Li="__reactEvents$"+Er,Xg="__reactListeners$"+Er,Yg="__reactHandles$"+Er;function wn(e){var t=e[Et];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Mt]||n[Et]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=lc(e);e!==null;){if(n=e[Et])return n;e=lc(e)}return t}e=n,n=e.parentNode}return null}function ko(e){return e=e[Et]||e[Mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Hn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function Js(e){return e[fo]||null}var zi=[],Kn=-1;function dn(e){return{current:e}}function oe(e){0>Kn||(e.current=zi[Kn],zi[Kn]=null,Kn--)}function ee(e,t){Kn++,zi[Kn]=e.current,e.current=t}var un={},Le=dn(un),We=dn(!1),Tn=un;function mr(e,t){var n=e.type.contextTypes;if(!n)return un;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Qe(e){return e=e.childContextTypes,e!=null}function Rs(){oe(We),oe(Le)}function ic(e,t,n){if(Le.current!==un)throw Error(P(168));ee(Le,t),ee(We,n)}function kf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(P(108,zv(e)||"Unknown",o));return ce({},n,r)}function bs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||un,Tn=Le.current,ee(Le,e),ee(We,We.current),!0}function ac(e,t,n){var r=e.stateNode;if(!r)throw Error(P(169));n?(e=kf(e,t,Tn),r.__reactInternalMemoizedMergedChildContext=e,oe(We),oe(Le),ee(Le,e)):oe(We),ee(We,n)}var bt=null,el=!1,Al=!1;function Tf(e){bt===null?bt=[e]:bt.push(e)}function qg(e){el=!0,Tf(e)}function fn(){if(!Al&&bt!==null){Al=!0;var e=0,t=Y;try{var n=bt;for(Y=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}bt=null,el=!1}catch(o){throw bt!==null&&(bt=bt.slice(e+1)),qd(Ea,fn),o}finally{Y=t,Al=!1}}return null}var Gn=[],Xn=0,Is=null,Ls=0,rt=[],ot=0,Pn=null,It=1,Lt="";function gn(e,t){Gn[Xn++]=Ls,Gn[Xn++]=Is,Is=e,Ls=t}function Pf(e,t,n){rt[ot++]=It,rt[ot++]=Lt,rt[ot++]=Pn,Pn=e;var r=It;e=Lt;var o=32-gt(r)-1;r&=~(1<<o),n+=1;var s=32-gt(t)+o;if(30<s){var l=o-o%5;s=(r&(1<<l)-1).toString(32),r>>=l,o-=l,It=1<<32-gt(t)+o|n<<o|r,Lt=s+e}else It=1<<s|n<<o|r,Lt=e}function Ia(e){e.return!==null&&(gn(e,1),Pf(e,1,0))}function La(e){for(;e===Is;)Is=Gn[--Xn],Gn[Xn]=null,Ls=Gn[--Xn],Gn[Xn]=null;for(;e===Pn;)Pn=rt[--ot],rt[ot]=null,Lt=rt[--ot],rt[ot]=null,It=rt[--ot],rt[ot]=null}var qe=null,Ye=null,se=!1,pt=null;function _f(e,t){var n=st(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function uc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,qe=e,Ye=tn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,qe=e,Ye=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Pn!==null?{id:It,overflow:Lt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=st(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,qe=e,Ye=null,!0):!1;default:return!1}}function Di(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Oi(e){if(se){var t=Ye;if(t){var n=t;if(!uc(e,t)){if(Di(e))throw Error(P(418));t=tn(n.nextSibling);var r=qe;t&&uc(e,t)?_f(r,n):(e.flags=e.flags&-4097|2,se=!1,qe=e)}}else{if(Di(e))throw Error(P(418));e.flags=e.flags&-4097|2,se=!1,qe=e}}}function cc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;qe=e}function Ko(e){if(e!==qe)return!1;if(!se)return cc(e),se=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!bi(e.type,e.memoizedProps)),t&&(t=Ye)){if(Di(e))throw Rf(),Error(P(418));for(;t;)_f(e,t),t=tn(t.nextSibling)}if(cc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ye=tn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ye=null}}else Ye=qe?tn(e.stateNode.nextSibling):null;return!0}function Rf(){for(var e=Ye;e;)e=tn(e.nextSibling)}function hr(){Ye=qe=null,se=!1}function za(e){pt===null?pt=[e]:pt.push(e)}var Zg=$t.ReactCurrentBatchConfig;function Dr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(P(309));var r=n.stateNode}if(!r)throw Error(P(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(l){var a=o.refs;l===null?delete a[s]:a[s]=l},t._stringRef=s,t)}if(typeof e!="string")throw Error(P(284));if(!n._owner)throw Error(P(290,e))}return e}function Go(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function dc(e){var t=e._init;return t(e._payload)}function bf(e){function t(h,f){if(e){var v=h.deletions;v===null?(h.deletions=[f],h.flags|=16):v.push(f)}}function n(h,f){if(!e)return null;for(;f!==null;)t(h,f),f=f.sibling;return null}function r(h,f){for(h=new Map;f!==null;)f.key!==null?h.set(f.key,f):h.set(f.index,f),f=f.sibling;return h}function o(h,f){return h=sn(h,f),h.index=0,h.sibling=null,h}function s(h,f,v){return h.index=v,e?(v=h.alternate,v!==null?(v=v.index,v<f?(h.flags|=2,f):v):(h.flags|=2,f)):(h.flags|=1048576,f)}function l(h){return e&&h.alternate===null&&(h.flags|=2),h}function a(h,f,v,S){return f===null||f.tag!==6?(f=Ql(v,h.mode,S),f.return=h,f):(f=o(f,v),f.return=h,f)}function u(h,f,v,S){var E=v.type;return E===Bn?m(h,f,v.props.children,S,v.key):f!==null&&(f.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===Qt&&dc(E)===f.type)?(S=o(f,v.props),S.ref=Dr(h,f,v),S.return=h,S):(S=vs(v.type,v.key,v.props,null,h.mode,S),S.ref=Dr(h,f,v),S.return=h,S)}function c(h,f,v,S){return f===null||f.tag!==4||f.stateNode.containerInfo!==v.containerInfo||f.stateNode.implementation!==v.implementation?(f=Hl(v,h.mode,S),f.return=h,f):(f=o(f,v.children||[]),f.return=h,f)}function m(h,f,v,S,E){return f===null||f.tag!==7?(f=Cn(v,h.mode,S,E),f.return=h,f):(f=o(f,v),f.return=h,f)}function p(h,f,v){if(typeof f=="string"&&f!==""||typeof f=="number")return f=Ql(""+f,h.mode,v),f.return=h,f;if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Mo:return v=vs(f.type,f.key,f.props,null,h.mode,v),v.ref=Dr(h,null,f),v.return=h,v;case Un:return f=Hl(f,h.mode,v),f.return=h,f;case Qt:var S=f._init;return p(h,S(f._payload),v)}if(Ur(f)||Rr(f))return f=Cn(f,h.mode,v,null),f.return=h,f;Go(h,f)}return null}function g(h,f,v,S){var E=f!==null?f.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return E!==null?null:a(h,f,""+v,S);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Mo:return v.key===E?u(h,f,v,S):null;case Un:return v.key===E?c(h,f,v,S):null;case Qt:return E=v._init,g(h,f,E(v._payload),S)}if(Ur(v)||Rr(v))return E!==null?null:m(h,f,v,S,null);Go(h,v)}return null}function w(h,f,v,S,E){if(typeof S=="string"&&S!==""||typeof S=="number")return h=h.get(v)||null,a(f,h,""+S,E);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Mo:return h=h.get(S.key===null?v:S.key)||null,u(f,h,S,E);case Un:return h=h.get(S.key===null?v:S.key)||null,c(f,h,S,E);case Qt:var T=S._init;return w(h,f,v,T(S._payload),E)}if(Ur(S)||Rr(S))return h=h.get(v)||null,m(f,h,S,E,null);Go(f,S)}return null}function j(h,f,v,S){for(var E=null,T=null,C=f,_=f=0,z=null;C!==null&&_<v.length;_++){C.index>_?(z=C,C=null):z=C.sibling;var O=g(h,C,v[_],S);if(O===null){C===null&&(C=z);break}e&&C&&O.alternate===null&&t(h,C),f=s(O,f,_),T===null?E=O:T.sibling=O,T=O,C=z}if(_===v.length)return n(h,C),se&&gn(h,_),E;if(C===null){for(;_<v.length;_++)C=p(h,v[_],S),C!==null&&(f=s(C,f,_),T===null?E=C:T.sibling=C,T=C);return se&&gn(h,_),E}for(C=r(h,C);_<v.length;_++)z=w(C,h,_,v[_],S),z!==null&&(e&&z.alternate!==null&&C.delete(z.key===null?_:z.key),f=s(z,f,_),T===null?E=z:T.sibling=z,T=z);return e&&C.forEach(function(Q){return t(h,Q)}),se&&gn(h,_),E}function y(h,f,v,S){var E=Rr(v);if(typeof E!="function")throw Error(P(150));if(v=E.call(v),v==null)throw Error(P(151));for(var T=E=null,C=f,_=f=0,z=null,O=v.next();C!==null&&!O.done;_++,O=v.next()){C.index>_?(z=C,C=null):z=C.sibling;var Q=g(h,C,O.value,S);if(Q===null){C===null&&(C=z);break}e&&C&&Q.alternate===null&&t(h,C),f=s(Q,f,_),T===null?E=Q:T.sibling=Q,T=Q,C=z}if(O.done)return n(h,C),se&&gn(h,_),E;if(C===null){for(;!O.done;_++,O=v.next())O=p(h,O.value,S),O!==null&&(f=s(O,f,_),T===null?E=O:T.sibling=O,T=O);return se&&gn(h,_),E}for(C=r(h,C);!O.done;_++,O=v.next())O=w(C,h,_,O.value,S),O!==null&&(e&&O.alternate!==null&&C.delete(O.key===null?_:O.key),f=s(O,f,_),T===null?E=O:T.sibling=O,T=O);return e&&C.forEach(function(F){return t(h,F)}),se&&gn(h,_),E}function x(h,f,v,S){if(typeof v=="object"&&v!==null&&v.type===Bn&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Mo:e:{for(var E=v.key,T=f;T!==null;){if(T.key===E){if(E=v.type,E===Bn){if(T.tag===7){n(h,T.sibling),f=o(T,v.props.children),f.return=h,h=f;break e}}else if(T.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===Qt&&dc(E)===T.type){n(h,T.sibling),f=o(T,v.props),f.ref=Dr(h,T,v),f.return=h,h=f;break e}n(h,T);break}else t(h,T);T=T.sibling}v.type===Bn?(f=Cn(v.props.children,h.mode,S,v.key),f.return=h,h=f):(S=vs(v.type,v.key,v.props,null,h.mode,S),S.ref=Dr(h,f,v),S.return=h,h=S)}return l(h);case Un:e:{for(T=v.key;f!==null;){if(f.key===T)if(f.tag===4&&f.stateNode.containerInfo===v.containerInfo&&f.stateNode.implementation===v.implementation){n(h,f.sibling),f=o(f,v.children||[]),f.return=h,h=f;break e}else{n(h,f);break}else t(h,f);f=f.sibling}f=Hl(v,h.mode,S),f.return=h,h=f}return l(h);case Qt:return T=v._init,x(h,f,T(v._payload),S)}if(Ur(v))return j(h,f,v,S);if(Rr(v))return y(h,f,v,S);Go(h,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,f!==null&&f.tag===6?(n(h,f.sibling),f=o(f,v),f.return=h,h=f):(n(h,f),f=Ql(v,h.mode,S),f.return=h,h=f),l(h)):n(h,f)}return x}var vr=bf(!0),If=bf(!1),zs=dn(null),Ds=null,Yn=null,Da=null;function Oa(){Da=Yn=Ds=null}function Ma(e){var t=zs.current;oe(zs),e._currentValue=t}function Mi(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function sr(e,t){Ds=e,Da=Yn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ve=!0),e.firstContext=null)}function it(e){var t=e._currentValue;if(Da!==e)if(e={context:e,memoizedValue:t,next:null},Yn===null){if(Ds===null)throw Error(P(308));Yn=e,Ds.dependencies={lanes:0,firstContext:e}}else Yn=Yn.next=e;return t}var Sn=null;function Aa(e){Sn===null?Sn=[e]:Sn.push(e)}function Lf(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Aa(t)):(n.next=o.next,o.next=n),t.interleaved=n,At(e,r)}function At(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ht=!1;function Fa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function zf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Dt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function nn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,W&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,At(e,n)}return o=r.interleaved,o===null?(t.next=t,Aa(r)):(t.next=o.next,o.next=t),r.interleaved=t,At(e,n)}function cs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ca(e,n)}}function fc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=l:s=s.next=l,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Os(e,t,n,r){var o=e.updateQueue;Ht=!1;var s=o.firstBaseUpdate,l=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var u=a,c=u.next;u.next=null,l===null?s=c:l.next=c,l=u;var m=e.alternate;m!==null&&(m=m.updateQueue,a=m.lastBaseUpdate,a!==l&&(a===null?m.firstBaseUpdate=c:a.next=c,m.lastBaseUpdate=u))}if(s!==null){var p=o.baseState;l=0,m=c=u=null,a=s;do{var g=a.lane,w=a.eventTime;if((r&g)===g){m!==null&&(m=m.next={eventTime:w,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var j=e,y=a;switch(g=t,w=n,y.tag){case 1:if(j=y.payload,typeof j=="function"){p=j.call(w,p,g);break e}p=j;break e;case 3:j.flags=j.flags&-65537|128;case 0:if(j=y.payload,g=typeof j=="function"?j.call(w,p,g):j,g==null)break e;p=ce({},p,g);break e;case 2:Ht=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,g=o.effects,g===null?o.effects=[a]:g.push(a))}else w={eventTime:w,lane:g,tag:a.tag,payload:a.payload,callback:a.callback,next:null},m===null?(c=m=w,u=p):m=m.next=w,l|=g;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;g=a,a=g.next,g.next=null,o.lastBaseUpdate=g,o.shared.pending=null}}while(!0);if(m===null&&(u=p),o.baseState=u,o.firstBaseUpdate=c,o.lastBaseUpdate=m,t=o.shared.interleaved,t!==null){o=t;do l|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);Rn|=l,e.lanes=l,e.memoizedState=p}}function pc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(P(191,o));o.call(r)}}}var To={},kt=dn(To),po=dn(To),mo=dn(To);function jn(e){if(e===To)throw Error(P(174));return e}function $a(e,t){switch(ee(mo,t),ee(po,e),ee(kt,To),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:gi(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=gi(t,e)}oe(kt),ee(kt,t)}function gr(){oe(kt),oe(po),oe(mo)}function Df(e){jn(mo.current);var t=jn(kt.current),n=gi(t,e.type);t!==n&&(ee(po,e),ee(kt,n))}function Ua(e){po.current===e&&(oe(kt),oe(po))}var ae=dn(0);function Ms(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Fl=[];function Ba(){for(var e=0;e<Fl.length;e++)Fl[e]._workInProgressVersionPrimary=null;Fl.length=0}var ds=$t.ReactCurrentDispatcher,$l=$t.ReactCurrentBatchConfig,_n=0,ue=null,ve=null,xe=null,As=!1,Xr=!1,ho=0,Jg=0;function _e(){throw Error(P(321))}function Va(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!xt(e[n],t[n]))return!1;return!0}function Wa(e,t,n,r,o,s){if(_n=s,ue=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ds.current=e===null||e.memoizedState===null?ry:oy,e=n(r,o),Xr){s=0;do{if(Xr=!1,ho=0,25<=s)throw Error(P(301));s+=1,xe=ve=null,t.updateQueue=null,ds.current=sy,e=n(r,o)}while(Xr)}if(ds.current=Fs,t=ve!==null&&ve.next!==null,_n=0,xe=ve=ue=null,As=!1,t)throw Error(P(300));return e}function Qa(){var e=ho!==0;return ho=0,e}function jt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return xe===null?ue.memoizedState=xe=e:xe=xe.next=e,xe}function at(){if(ve===null){var e=ue.alternate;e=e!==null?e.memoizedState:null}else e=ve.next;var t=xe===null?ue.memoizedState:xe.next;if(t!==null)xe=t,ve=e;else{if(e===null)throw Error(P(310));ve=e,e={memoizedState:ve.memoizedState,baseState:ve.baseState,baseQueue:ve.baseQueue,queue:ve.queue,next:null},xe===null?ue.memoizedState=xe=e:xe=xe.next=e}return xe}function vo(e,t){return typeof t=="function"?t(e):t}function Ul(e){var t=at(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=ve,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var l=o.next;o.next=s.next,s.next=l}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var a=l=null,u=null,c=s;do{var m=c.lane;if((_n&m)===m)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var p={lane:m,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=p,l=r):u=u.next=p,ue.lanes|=m,Rn|=m}c=c.next}while(c!==null&&c!==s);u===null?l=r:u.next=a,xt(r,t.memoizedState)||(Ve=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,ue.lanes|=s,Rn|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Bl(e){var t=at(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var l=o=o.next;do s=e(s,l.action),l=l.next;while(l!==o);xt(s,t.memoizedState)||(Ve=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Of(){}function Mf(e,t){var n=ue,r=at(),o=t(),s=!xt(r.memoizedState,o);if(s&&(r.memoizedState=o,Ve=!0),r=r.queue,Ha($f.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||xe!==null&&xe.memoizedState.tag&1){if(n.flags|=2048,go(9,Ff.bind(null,n,r,o,t),void 0,null),Se===null)throw Error(P(349));_n&30||Af(n,t,o)}return o}function Af(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Ff(e,t,n,r){t.value=n,t.getSnapshot=r,Uf(t)&&Bf(e)}function $f(e,t,n){return n(function(){Uf(t)&&Bf(e)})}function Uf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!xt(e,n)}catch{return!0}}function Bf(e){var t=At(e,1);t!==null&&yt(t,e,1,-1)}function mc(e){var t=jt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:vo,lastRenderedState:e},t.queue=e,e=e.dispatch=ny.bind(null,ue,e),[t.memoizedState,e]}function go(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Vf(){return at().memoizedState}function fs(e,t,n,r){var o=jt();ue.flags|=e,o.memoizedState=go(1|t,n,void 0,r===void 0?null:r)}function tl(e,t,n,r){var o=at();r=r===void 0?null:r;var s=void 0;if(ve!==null){var l=ve.memoizedState;if(s=l.destroy,r!==null&&Va(r,l.deps)){o.memoizedState=go(t,n,s,r);return}}ue.flags|=e,o.memoizedState=go(1|t,n,s,r)}function hc(e,t){return fs(8390656,8,e,t)}function Ha(e,t){return tl(2048,8,e,t)}function Wf(e,t){return tl(4,2,e,t)}function Qf(e,t){return tl(4,4,e,t)}function Hf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Kf(e,t,n){return n=n!=null?n.concat([e]):null,tl(4,4,Hf.bind(null,t,e),n)}function Ka(){}function Gf(e,t){var n=at();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Va(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Xf(e,t){var n=at();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Va(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Yf(e,t,n){return _n&21?(xt(n,t)||(n=ef(),ue.lanes|=n,Rn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ve=!0),e.memoizedState=n)}function ey(e,t){var n=Y;Y=n!==0&&4>n?n:4,e(!0);var r=$l.transition;$l.transition={};try{e(!1),t()}finally{Y=n,$l.transition=r}}function qf(){return at().memoizedState}function ty(e,t,n){var r=on(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Zf(e))Jf(t,n);else if(n=Lf(e,t,n,r),n!==null){var o=Ae();yt(n,e,r,o),ep(n,t,r)}}function ny(e,t,n){var r=on(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Zf(e))Jf(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var l=t.lastRenderedState,a=s(l,n);if(o.hasEagerState=!0,o.eagerState=a,xt(a,l)){var u=t.interleaved;u===null?(o.next=o,Aa(t)):(o.next=u.next,u.next=o),t.interleaved=o;return}}catch{}finally{}n=Lf(e,t,o,r),n!==null&&(o=Ae(),yt(n,e,r,o),ep(n,t,r))}}function Zf(e){var t=e.alternate;return e===ue||t!==null&&t===ue}function Jf(e,t){Xr=As=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function ep(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ca(e,n)}}var Fs={readContext:it,useCallback:_e,useContext:_e,useEffect:_e,useImperativeHandle:_e,useInsertionEffect:_e,useLayoutEffect:_e,useMemo:_e,useReducer:_e,useRef:_e,useState:_e,useDebugValue:_e,useDeferredValue:_e,useTransition:_e,useMutableSource:_e,useSyncExternalStore:_e,useId:_e,unstable_isNewReconciler:!1},ry={readContext:it,useCallback:function(e,t){return jt().memoizedState=[e,t===void 0?null:t],e},useContext:it,useEffect:hc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,fs(4194308,4,Hf.bind(null,t,e),n)},useLayoutEffect:function(e,t){return fs(4194308,4,e,t)},useInsertionEffect:function(e,t){return fs(4,2,e,t)},useMemo:function(e,t){var n=jt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=jt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ty.bind(null,ue,e),[r.memoizedState,e]},useRef:function(e){var t=jt();return e={current:e},t.memoizedState=e},useState:mc,useDebugValue:Ka,useDeferredValue:function(e){return jt().memoizedState=e},useTransition:function(){var e=mc(!1),t=e[0];return e=ey.bind(null,e[1]),jt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ue,o=jt();if(se){if(n===void 0)throw Error(P(407));n=n()}else{if(n=t(),Se===null)throw Error(P(349));_n&30||Af(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,hc($f.bind(null,r,s,e),[e]),r.flags|=2048,go(9,Ff.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=jt(),t=Se.identifierPrefix;if(se){var n=Lt,r=It;n=(r&~(1<<32-gt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ho++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Jg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},oy={readContext:it,useCallback:Gf,useContext:it,useEffect:Ha,useImperativeHandle:Kf,useInsertionEffect:Wf,useLayoutEffect:Qf,useMemo:Xf,useReducer:Ul,useRef:Vf,useState:function(){return Ul(vo)},useDebugValue:Ka,useDeferredValue:function(e){var t=at();return Yf(t,ve.memoizedState,e)},useTransition:function(){var e=Ul(vo)[0],t=at().memoizedState;return[e,t]},useMutableSource:Of,useSyncExternalStore:Mf,useId:qf,unstable_isNewReconciler:!1},sy={readContext:it,useCallback:Gf,useContext:it,useEffect:Ha,useImperativeHandle:Kf,useInsertionEffect:Wf,useLayoutEffect:Qf,useMemo:Xf,useReducer:Bl,useRef:Vf,useState:function(){return Bl(vo)},useDebugValue:Ka,useDeferredValue:function(e){var t=at();return ve===null?t.memoizedState=e:Yf(t,ve.memoizedState,e)},useTransition:function(){var e=Bl(vo)[0],t=at().memoizedState;return[e,t]},useMutableSource:Of,useSyncExternalStore:Mf,useId:qf,unstable_isNewReconciler:!1};function dt(e,t){if(e&&e.defaultProps){t=ce({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ai(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ce({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var nl={isMounted:function(e){return(e=e._reactInternals)?zn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ae(),o=on(e),s=Dt(r,o);s.payload=t,n!=null&&(s.callback=n),t=nn(e,s,o),t!==null&&(yt(t,e,o,r),cs(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ae(),o=on(e),s=Dt(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=nn(e,s,o),t!==null&&(yt(t,e,o,r),cs(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ae(),r=on(e),o=Dt(n,r);o.tag=2,t!=null&&(o.callback=t),t=nn(e,o,r),t!==null&&(yt(t,e,r,n),cs(t,e,r))}};function vc(e,t,n,r,o,s,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,l):t.prototype&&t.prototype.isPureReactComponent?!ao(n,r)||!ao(o,s):!0}function tp(e,t,n){var r=!1,o=un,s=t.contextType;return typeof s=="object"&&s!==null?s=it(s):(o=Qe(t)?Tn:Le.current,r=t.contextTypes,s=(r=r!=null)?mr(e,o):un),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=nl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function gc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&nl.enqueueReplaceState(t,t.state,null)}function Fi(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Fa(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=it(s):(s=Qe(t)?Tn:Le.current,o.context=mr(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Ai(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&nl.enqueueReplaceState(o,o.state,null),Os(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function yr(e,t){try{var n="",r=t;do n+=Lv(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function Vl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function $i(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var ly=typeof WeakMap=="function"?WeakMap:Map;function np(e,t,n){n=Dt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Us||(Us=!0,Yi=r),$i(e,t)},n}function rp(e,t,n){n=Dt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){$i(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){$i(e,t),typeof r!="function"&&(rn===null?rn=new Set([this]):rn.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function yc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new ly;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=wy.bind(null,e,t,n),t.then(e,e))}function xc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function wc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Dt(-1,1),t.tag=2,nn(n,t,1))),n.lanes|=1),e)}var iy=$t.ReactCurrentOwner,Ve=!1;function ze(e,t,n,r){t.child=e===null?If(t,null,n,r):vr(t,e.child,n,r)}function Sc(e,t,n,r,o){n=n.render;var s=t.ref;return sr(t,o),r=Wa(e,t,n,r,s,o),n=Qa(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ft(e,t,o)):(se&&n&&Ia(t),t.flags|=1,ze(e,t,r,o),t.child)}function jc(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!tu(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,op(e,t,s,r,o)):(e=vs(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var l=s.memoizedProps;if(n=n.compare,n=n!==null?n:ao,n(l,r)&&e.ref===t.ref)return Ft(e,t,o)}return t.flags|=1,e=sn(s,r),e.ref=t.ref,e.return=t,t.child=e}function op(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(ao(s,r)&&e.ref===t.ref)if(Ve=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(Ve=!0);else return t.lanes=e.lanes,Ft(e,t,o)}return Ui(e,t,n,r,o)}function sp(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ee(Zn,Xe),Xe|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ee(Zn,Xe),Xe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,ee(Zn,Xe),Xe|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,ee(Zn,Xe),Xe|=r;return ze(e,t,o,n),t.child}function lp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ui(e,t,n,r,o){var s=Qe(n)?Tn:Le.current;return s=mr(t,s),sr(t,o),n=Wa(e,t,n,r,s,o),r=Qa(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ft(e,t,o)):(se&&r&&Ia(t),t.flags|=1,ze(e,t,n,o),t.child)}function Ec(e,t,n,r,o){if(Qe(n)){var s=!0;bs(t)}else s=!1;if(sr(t,o),t.stateNode===null)ps(e,t),tp(t,n,r),Fi(t,n,r,o),r=!0;else if(e===null){var l=t.stateNode,a=t.memoizedProps;l.props=a;var u=l.context,c=n.contextType;typeof c=="object"&&c!==null?c=it(c):(c=Qe(n)?Tn:Le.current,c=mr(t,c));var m=n.getDerivedStateFromProps,p=typeof m=="function"||typeof l.getSnapshotBeforeUpdate=="function";p||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(a!==r||u!==c)&&gc(t,l,r,c),Ht=!1;var g=t.memoizedState;l.state=g,Os(t,r,l,o),u=t.memoizedState,a!==r||g!==u||We.current||Ht?(typeof m=="function"&&(Ai(t,n,m,r),u=t.memoizedState),(a=Ht||vc(t,n,a,r,g,u,c))?(p||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=c,r=a):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,zf(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:dt(t.type,a),l.props=c,p=t.pendingProps,g=l.context,u=n.contextType,typeof u=="object"&&u!==null?u=it(u):(u=Qe(n)?Tn:Le.current,u=mr(t,u));var w=n.getDerivedStateFromProps;(m=typeof w=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(a!==p||g!==u)&&gc(t,l,r,u),Ht=!1,g=t.memoizedState,l.state=g,Os(t,r,l,o);var j=t.memoizedState;a!==p||g!==j||We.current||Ht?(typeof w=="function"&&(Ai(t,n,w,r),j=t.memoizedState),(c=Ht||vc(t,n,c,r,g,j,u)||!1)?(m||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,j,u),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,j,u)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=j),l.props=r,l.state=j,l.context=u,r=c):(typeof l.componentDidUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),r=!1)}return Bi(e,t,n,r,s,o)}function Bi(e,t,n,r,o,s){lp(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return o&&ac(t,n,!1),Ft(e,t,s);r=t.stateNode,iy.current=t;var a=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=vr(t,e.child,null,s),t.child=vr(t,null,a,s)):ze(e,t,a,s),t.memoizedState=r.state,o&&ac(t,n,!0),t.child}function ip(e){var t=e.stateNode;t.pendingContext?ic(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ic(e,t.context,!1),$a(e,t.containerInfo)}function Cc(e,t,n,r,o){return hr(),za(o),t.flags|=256,ze(e,t,n,r),t.child}var Vi={dehydrated:null,treeContext:null,retryLane:0};function Wi(e){return{baseLanes:e,cachePool:null,transitions:null}}function ap(e,t,n){var r=t.pendingProps,o=ae.current,s=!1,l=(t.flags&128)!==0,a;if((a=l)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ee(ae,o&1),e===null)return Oi(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,s?(r=t.mode,s=t.child,l={mode:"hidden",children:l},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=l):s=sl(l,r,0,null),e=Cn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Wi(n),t.memoizedState=Vi,e):Ga(t,l));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return ay(e,t,l,r,a,o,n);if(s){s=r.fallback,l=t.mode,o=e.child,a=o.sibling;var u={mode:"hidden",children:r.children};return!(l&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=sn(o,u),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?s=sn(a,s):(s=Cn(s,l,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,l=e.child.memoizedState,l=l===null?Wi(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=Vi,r}return s=e.child,e=s.sibling,r=sn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ga(e,t){return t=sl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Xo(e,t,n,r){return r!==null&&za(r),vr(t,e.child,null,n),e=Ga(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ay(e,t,n,r,o,s,l){if(n)return t.flags&256?(t.flags&=-257,r=Vl(Error(P(422))),Xo(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=sl({mode:"visible",children:r.children},o,0,null),s=Cn(s,o,l,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&vr(t,e.child,null,l),t.child.memoizedState=Wi(l),t.memoizedState=Vi,s);if(!(t.mode&1))return Xo(e,t,l,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(P(419)),r=Vl(s,r,void 0),Xo(e,t,l,r)}if(a=(l&e.childLanes)!==0,Ve||a){if(r=Se,r!==null){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|l)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,At(e,o),yt(r,e,o,-1))}return eu(),r=Vl(Error(P(421))),Xo(e,t,l,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Sy.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,Ye=tn(o.nextSibling),qe=t,se=!0,pt=null,e!==null&&(rt[ot++]=It,rt[ot++]=Lt,rt[ot++]=Pn,It=e.id,Lt=e.overflow,Pn=t),t=Ga(t,r.children),t.flags|=4096,t)}function Nc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Mi(e.return,t,n)}function Wl(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function up(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if(ze(e,t,r.children,n),r=ae.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Nc(e,n,t);else if(e.tag===19)Nc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ee(ae,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ms(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Wl(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ms(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Wl(t,!0,n,null,s);break;case"together":Wl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ps(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ft(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Rn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,n=sn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=sn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function uy(e,t,n){switch(t.tag){case 3:ip(t),hr();break;case 5:Df(t);break;case 1:Qe(t.type)&&bs(t);break;case 4:$a(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ee(zs,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ee(ae,ae.current&1),t.flags|=128,null):n&t.child.childLanes?ap(e,t,n):(ee(ae,ae.current&1),e=Ft(e,t,n),e!==null?e.sibling:null);ee(ae,ae.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return up(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ee(ae,ae.current),r)break;return null;case 22:case 23:return t.lanes=0,sp(e,t,n)}return Ft(e,t,n)}var cp,Qi,dp,fp;cp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Qi=function(){};dp=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,jn(kt.current);var s=null;switch(n){case"input":o=pi(e,o),r=pi(e,r),s=[];break;case"select":o=ce({},o,{value:void 0}),r=ce({},r,{value:void 0}),s=[];break;case"textarea":o=vi(e,o),r=vi(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=_s)}yi(n,r);var l;n=null;for(c in o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&o[c]!=null)if(c==="style"){var a=o[c];for(l in a)a.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(to.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in r){var u=r[c];if(a=o!=null?o[c]:void 0,r.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(l in a)!a.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in u)u.hasOwnProperty(l)&&a[l]!==u[l]&&(n||(n={}),n[l]=u[l])}else n||(s||(s=[]),s.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(s=s||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(s=s||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(to.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&re("scroll",e),s||a===u||(s=[])):(s=s||[]).push(c,u))}n&&(s=s||[]).push("style",n);var c=s;(t.updateQueue=c)&&(t.flags|=4)}};fp=function(e,t,n,r){n!==r&&(t.flags|=4)};function Or(e,t){if(!se)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Re(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function cy(e,t,n){var r=t.pendingProps;switch(La(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Re(t),null;case 1:return Qe(t.type)&&Rs(),Re(t),null;case 3:return r=t.stateNode,gr(),oe(We),oe(Le),Ba(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ko(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,pt!==null&&(Ji(pt),pt=null))),Qi(e,t),Re(t),null;case 5:Ua(t);var o=jn(mo.current);if(n=t.type,e!==null&&t.stateNode!=null)dp(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(P(166));return Re(t),null}if(e=jn(kt.current),Ko(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[Et]=t,r[fo]=s,e=(t.mode&1)!==0,n){case"dialog":re("cancel",r),re("close",r);break;case"iframe":case"object":case"embed":re("load",r);break;case"video":case"audio":for(o=0;o<Vr.length;o++)re(Vr[o],r);break;case"source":re("error",r);break;case"img":case"image":case"link":re("error",r),re("load",r);break;case"details":re("toggle",r);break;case"input":zu(r,s),re("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},re("invalid",r);break;case"textarea":Ou(r,s),re("invalid",r)}yi(n,s),o=null;for(var l in s)if(s.hasOwnProperty(l)){var a=s[l];l==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&Ho(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&Ho(r.textContent,a,e),o=["children",""+a]):to.hasOwnProperty(l)&&a!=null&&l==="onScroll"&&re("scroll",r)}switch(n){case"input":Ao(r),Du(r,s,!0);break;case"textarea":Ao(r),Mu(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=_s)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Fd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[Et]=t,e[fo]=r,cp(e,t,!1,!1),t.stateNode=e;e:{switch(l=xi(n,r),n){case"dialog":re("cancel",e),re("close",e),o=r;break;case"iframe":case"object":case"embed":re("load",e),o=r;break;case"video":case"audio":for(o=0;o<Vr.length;o++)re(Vr[o],e);o=r;break;case"source":re("error",e),o=r;break;case"img":case"image":case"link":re("error",e),re("load",e),o=r;break;case"details":re("toggle",e),o=r;break;case"input":zu(e,r),o=pi(e,r),re("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ce({},r,{value:void 0}),re("invalid",e);break;case"textarea":Ou(e,r),o=vi(e,r),re("invalid",e);break;default:o=r}yi(n,o),a=o;for(s in a)if(a.hasOwnProperty(s)){var u=a[s];s==="style"?Bd(e,u):s==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&$d(e,u)):s==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&no(e,u):typeof u=="number"&&no(e,""+u):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(to.hasOwnProperty(s)?u!=null&&s==="onScroll"&&re("scroll",e):u!=null&&ya(e,s,u,l))}switch(n){case"input":Ao(e),Du(e,r,!1);break;case"textarea":Ao(e),Mu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+an(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?tr(e,!!r.multiple,s,!1):r.defaultValue!=null&&tr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=_s)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Re(t),null;case 6:if(e&&t.stateNode!=null)fp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(P(166));if(n=jn(mo.current),jn(kt.current),Ko(t)){if(r=t.stateNode,n=t.memoizedProps,r[Et]=t,(s=r.nodeValue!==n)&&(e=qe,e!==null))switch(e.tag){case 3:Ho(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ho(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Et]=t,t.stateNode=r}return Re(t),null;case 13:if(oe(ae),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(se&&Ye!==null&&t.mode&1&&!(t.flags&128))Rf(),hr(),t.flags|=98560,s=!1;else if(s=Ko(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(P(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(P(317));s[Et]=t}else hr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Re(t),s=!1}else pt!==null&&(Ji(pt),pt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ae.current&1?ge===0&&(ge=3):eu())),t.updateQueue!==null&&(t.flags|=4),Re(t),null);case 4:return gr(),Qi(e,t),e===null&&uo(t.stateNode.containerInfo),Re(t),null;case 10:return Ma(t.type._context),Re(t),null;case 17:return Qe(t.type)&&Rs(),Re(t),null;case 19:if(oe(ae),s=t.memoizedState,s===null)return Re(t),null;if(r=(t.flags&128)!==0,l=s.rendering,l===null)if(r)Or(s,!1);else{if(ge!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Ms(e),l!==null){for(t.flags|=128,Or(s,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,l=s.alternate,l===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=l.childLanes,s.lanes=l.lanes,s.child=l.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=l.memoizedProps,s.memoizedState=l.memoizedState,s.updateQueue=l.updateQueue,s.type=l.type,e=l.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ee(ae,ae.current&1|2),t.child}e=e.sibling}s.tail!==null&&pe()>xr&&(t.flags|=128,r=!0,Or(s,!1),t.lanes=4194304)}else{if(!r)if(e=Ms(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Or(s,!0),s.tail===null&&s.tailMode==="hidden"&&!l.alternate&&!se)return Re(t),null}else 2*pe()-s.renderingStartTime>xr&&n!==1073741824&&(t.flags|=128,r=!0,Or(s,!1),t.lanes=4194304);s.isBackwards?(l.sibling=t.child,t.child=l):(n=s.last,n!==null?n.sibling=l:t.child=l,s.last=l)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=pe(),t.sibling=null,n=ae.current,ee(ae,r?n&1|2:n&1),t):(Re(t),null);case 22:case 23:return Ja(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Xe&1073741824&&(Re(t),t.subtreeFlags&6&&(t.flags|=8192)):Re(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function dy(e,t){switch(La(t),t.tag){case 1:return Qe(t.type)&&Rs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return gr(),oe(We),oe(Le),Ba(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ua(t),null;case 13:if(oe(ae),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));hr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return oe(ae),null;case 4:return gr(),null;case 10:return Ma(t.type._context),null;case 22:case 23:return Ja(),null;case 24:return null;default:return null}}var Yo=!1,be=!1,fy=typeof WeakSet=="function"?WeakSet:Set,I=null;function qn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){fe(e,t,r)}else n.current=null}function Hi(e,t,n){try{n()}catch(r){fe(e,t,r)}}var kc=!1;function py(e,t){if(_i=ks,e=gf(),ba(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var l=0,a=-1,u=-1,c=0,m=0,p=e,g=null;t:for(;;){for(var w;p!==n||o!==0&&p.nodeType!==3||(a=l+o),p!==s||r!==0&&p.nodeType!==3||(u=l+r),p.nodeType===3&&(l+=p.nodeValue.length),(w=p.firstChild)!==null;)g=p,p=w;for(;;){if(p===e)break t;if(g===n&&++c===o&&(a=l),g===s&&++m===r&&(u=l),(w=p.nextSibling)!==null)break;p=g,g=p.parentNode}p=w}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ri={focusedElem:e,selectionRange:n},ks=!1,I=t;I!==null;)if(t=I,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,I=e;else for(;I!==null;){t=I;try{var j=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(j!==null){var y=j.memoizedProps,x=j.memoizedState,h=t.stateNode,f=h.getSnapshotBeforeUpdate(t.elementType===t.type?y:dt(t.type,y),x);h.__reactInternalSnapshotBeforeUpdate=f}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(S){fe(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,I=e;break}I=t.return}return j=kc,kc=!1,j}function Yr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&Hi(t,n,s)}o=o.next}while(o!==r)}}function rl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ki(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function pp(e){var t=e.alternate;t!==null&&(e.alternate=null,pp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Et],delete t[fo],delete t[Li],delete t[Xg],delete t[Yg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function mp(e){return e.tag===5||e.tag===3||e.tag===4}function Tc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||mp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Gi(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=_s));else if(r!==4&&(e=e.child,e!==null))for(Gi(e,t,n),e=e.sibling;e!==null;)Gi(e,t,n),e=e.sibling}function Xi(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Xi(e,t,n),e=e.sibling;e!==null;)Xi(e,t,n),e=e.sibling}var Ce=null,ft=!1;function Ut(e,t,n){for(n=n.child;n!==null;)hp(e,t,n),n=n.sibling}function hp(e,t,n){if(Nt&&typeof Nt.onCommitFiberUnmount=="function")try{Nt.onCommitFiberUnmount(Xs,n)}catch{}switch(n.tag){case 5:be||qn(n,t);case 6:var r=Ce,o=ft;Ce=null,Ut(e,t,n),Ce=r,ft=o,Ce!==null&&(ft?(e=Ce,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ce.removeChild(n.stateNode));break;case 18:Ce!==null&&(ft?(e=Ce,n=n.stateNode,e.nodeType===8?Ml(e.parentNode,n):e.nodeType===1&&Ml(e,n),lo(e)):Ml(Ce,n.stateNode));break;case 4:r=Ce,o=ft,Ce=n.stateNode.containerInfo,ft=!0,Ut(e,t,n),Ce=r,ft=o;break;case 0:case 11:case 14:case 15:if(!be&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,l=s.destroy;s=s.tag,l!==void 0&&(s&2||s&4)&&Hi(n,t,l),o=o.next}while(o!==r)}Ut(e,t,n);break;case 1:if(!be&&(qn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){fe(n,t,a)}Ut(e,t,n);break;case 21:Ut(e,t,n);break;case 22:n.mode&1?(be=(r=be)||n.memoizedState!==null,Ut(e,t,n),be=r):Ut(e,t,n);break;default:Ut(e,t,n)}}function Pc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new fy),t.forEach(function(r){var o=jy.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function ct(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,l=t,a=l;e:for(;a!==null;){switch(a.tag){case 5:Ce=a.stateNode,ft=!1;break e;case 3:Ce=a.stateNode.containerInfo,ft=!0;break e;case 4:Ce=a.stateNode.containerInfo,ft=!0;break e}a=a.return}if(Ce===null)throw Error(P(160));hp(s,l,o),Ce=null,ft=!1;var u=o.alternate;u!==null&&(u.return=null),o.return=null}catch(c){fe(o,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)vp(t,e),t=t.sibling}function vp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ct(t,e),St(e),r&4){try{Yr(3,e,e.return),rl(3,e)}catch(y){fe(e,e.return,y)}try{Yr(5,e,e.return)}catch(y){fe(e,e.return,y)}}break;case 1:ct(t,e),St(e),r&512&&n!==null&&qn(n,n.return);break;case 5:if(ct(t,e),St(e),r&512&&n!==null&&qn(n,n.return),e.flags&32){var o=e.stateNode;try{no(o,"")}catch(y){fe(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,l=n!==null?n.memoizedProps:s,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&Md(o,s),xi(a,l);var c=xi(a,s);for(l=0;l<u.length;l+=2){var m=u[l],p=u[l+1];m==="style"?Bd(o,p):m==="dangerouslySetInnerHTML"?$d(o,p):m==="children"?no(o,p):ya(o,m,p,c)}switch(a){case"input":mi(o,s);break;case"textarea":Ad(o,s);break;case"select":var g=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var w=s.value;w!=null?tr(o,!!s.multiple,w,!1):g!==!!s.multiple&&(s.defaultValue!=null?tr(o,!!s.multiple,s.defaultValue,!0):tr(o,!!s.multiple,s.multiple?[]:"",!1))}o[fo]=s}catch(y){fe(e,e.return,y)}}break;case 6:if(ct(t,e),St(e),r&4){if(e.stateNode===null)throw Error(P(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(y){fe(e,e.return,y)}}break;case 3:if(ct(t,e),St(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{lo(t.containerInfo)}catch(y){fe(e,e.return,y)}break;case 4:ct(t,e),St(e);break;case 13:ct(t,e),St(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(qa=pe())),r&4&&Pc(e);break;case 22:if(m=n!==null&&n.memoizedState!==null,e.mode&1?(be=(c=be)||m,ct(t,e),be=c):ct(t,e),St(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!m&&e.mode&1)for(I=e,m=e.child;m!==null;){for(p=I=m;I!==null;){switch(g=I,w=g.child,g.tag){case 0:case 11:case 14:case 15:Yr(4,g,g.return);break;case 1:qn(g,g.return);var j=g.stateNode;if(typeof j.componentWillUnmount=="function"){r=g,n=g.return;try{t=r,j.props=t.memoizedProps,j.state=t.memoizedState,j.componentWillUnmount()}catch(y){fe(r,n,y)}}break;case 5:qn(g,g.return);break;case 22:if(g.memoizedState!==null){Rc(p);continue}}w!==null?(w.return=g,I=w):Rc(p)}m=m.sibling}e:for(m=null,p=e;;){if(p.tag===5){if(m===null){m=p;try{o=p.stateNode,c?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=p.stateNode,u=p.memoizedProps.style,l=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=Ud("display",l))}catch(y){fe(e,e.return,y)}}}else if(p.tag===6){if(m===null)try{p.stateNode.nodeValue=c?"":p.memoizedProps}catch(y){fe(e,e.return,y)}}else if((p.tag!==22&&p.tag!==23||p.memoizedState===null||p===e)&&p.child!==null){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;p.sibling===null;){if(p.return===null||p.return===e)break e;m===p&&(m=null),p=p.return}m===p&&(m=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:ct(t,e),St(e),r&4&&Pc(e);break;case 21:break;default:ct(t,e),St(e)}}function St(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(mp(n)){var r=n;break e}n=n.return}throw Error(P(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(no(o,""),r.flags&=-33);var s=Tc(e);Xi(e,s,o);break;case 3:case 4:var l=r.stateNode.containerInfo,a=Tc(e);Gi(e,a,l);break;default:throw Error(P(161))}}catch(u){fe(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function my(e,t,n){I=e,gp(e)}function gp(e,t,n){for(var r=(e.mode&1)!==0;I!==null;){var o=I,s=o.child;if(o.tag===22&&r){var l=o.memoizedState!==null||Yo;if(!l){var a=o.alternate,u=a!==null&&a.memoizedState!==null||be;a=Yo;var c=be;if(Yo=l,(be=u)&&!c)for(I=o;I!==null;)l=I,u=l.child,l.tag===22&&l.memoizedState!==null?bc(o):u!==null?(u.return=l,I=u):bc(o);for(;s!==null;)I=s,gp(s),s=s.sibling;I=o,Yo=a,be=c}_c(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,I=s):_c(e)}}function _c(e){for(;I!==null;){var t=I;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:be||rl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!be)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:dt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&pc(t,s,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}pc(t,l,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var m=c.memoizedState;if(m!==null){var p=m.dehydrated;p!==null&&lo(p)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}be||t.flags&512&&Ki(t)}catch(g){fe(t,t.return,g)}}if(t===e){I=null;break}if(n=t.sibling,n!==null){n.return=t.return,I=n;break}I=t.return}}function Rc(e){for(;I!==null;){var t=I;if(t===e){I=null;break}var n=t.sibling;if(n!==null){n.return=t.return,I=n;break}I=t.return}}function bc(e){for(;I!==null;){var t=I;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(u){fe(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(u){fe(t,o,u)}}var s=t.return;try{Ki(t)}catch(u){fe(t,s,u)}break;case 5:var l=t.return;try{Ki(t)}catch(u){fe(t,l,u)}}}catch(u){fe(t,t.return,u)}if(t===e){I=null;break}var a=t.sibling;if(a!==null){a.return=t.return,I=a;break}I=t.return}}var hy=Math.ceil,$s=$t.ReactCurrentDispatcher,Xa=$t.ReactCurrentOwner,lt=$t.ReactCurrentBatchConfig,W=0,Se=null,me=null,Ne=0,Xe=0,Zn=dn(0),ge=0,yo=null,Rn=0,ol=0,Ya=0,qr=null,Ue=null,qa=0,xr=1/0,Rt=null,Us=!1,Yi=null,rn=null,qo=!1,Yt=null,Bs=0,Zr=0,qi=null,ms=-1,hs=0;function Ae(){return W&6?pe():ms!==-1?ms:ms=pe()}function on(e){return e.mode&1?W&2&&Ne!==0?Ne&-Ne:Zg.transition!==null?(hs===0&&(hs=ef()),hs):(e=Y,e!==0||(e=window.event,e=e===void 0?16:af(e.type)),e):1}function yt(e,t,n,r){if(50<Zr)throw Zr=0,qi=null,Error(P(185));Co(e,n,r),(!(W&2)||e!==Se)&&(e===Se&&(!(W&2)&&(ol|=n),ge===4&&Gt(e,Ne)),He(e,r),n===1&&W===0&&!(t.mode&1)&&(xr=pe()+500,el&&fn()))}function He(e,t){var n=e.callbackNode;Zv(e,t);var r=Ns(e,e===Se?Ne:0);if(r===0)n!==null&&$u(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&$u(n),t===1)e.tag===0?qg(Ic.bind(null,e)):Tf(Ic.bind(null,e)),Kg(function(){!(W&6)&&fn()}),n=null;else{switch(tf(r)){case 1:n=Ea;break;case 4:n=Zd;break;case 16:n=Cs;break;case 536870912:n=Jd;break;default:n=Cs}n=Np(n,yp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function yp(e,t){if(ms=-1,hs=0,W&6)throw Error(P(327));var n=e.callbackNode;if(lr()&&e.callbackNode!==n)return null;var r=Ns(e,e===Se?Ne:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Vs(e,r);else{t=r;var o=W;W|=2;var s=wp();(Se!==e||Ne!==t)&&(Rt=null,xr=pe()+500,En(e,t));do try{yy();break}catch(a){xp(e,a)}while(!0);Oa(),$s.current=s,W=o,me!==null?t=0:(Se=null,Ne=0,t=ge)}if(t!==0){if(t===2&&(o=Ci(e),o!==0&&(r=o,t=Zi(e,o))),t===1)throw n=yo,En(e,0),Gt(e,r),He(e,pe()),n;if(t===6)Gt(e,r);else{if(o=e.current.alternate,!(r&30)&&!vy(o)&&(t=Vs(e,r),t===2&&(s=Ci(e),s!==0&&(r=s,t=Zi(e,s))),t===1))throw n=yo,En(e,0),Gt(e,r),He(e,pe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(P(345));case 2:yn(e,Ue,Rt);break;case 3:if(Gt(e,r),(r&130023424)===r&&(t=qa+500-pe(),10<t)){if(Ns(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ae(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Ii(yn.bind(null,e,Ue,Rt),t);break}yn(e,Ue,Rt);break;case 4:if(Gt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-gt(r);s=1<<l,l=t[l],l>o&&(o=l),r&=~s}if(r=o,r=pe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*hy(r/1960))-r,10<r){e.timeoutHandle=Ii(yn.bind(null,e,Ue,Rt),r);break}yn(e,Ue,Rt);break;case 5:yn(e,Ue,Rt);break;default:throw Error(P(329))}}}return He(e,pe()),e.callbackNode===n?yp.bind(null,e):null}function Zi(e,t){var n=qr;return e.current.memoizedState.isDehydrated&&(En(e,t).flags|=256),e=Vs(e,t),e!==2&&(t=Ue,Ue=n,t!==null&&Ji(t)),e}function Ji(e){Ue===null?Ue=e:Ue.push.apply(Ue,e)}function vy(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!xt(s(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Gt(e,t){for(t&=~Ya,t&=~ol,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-gt(t),r=1<<n;e[n]=-1,t&=~r}}function Ic(e){if(W&6)throw Error(P(327));lr();var t=Ns(e,0);if(!(t&1))return He(e,pe()),null;var n=Vs(e,t);if(e.tag!==0&&n===2){var r=Ci(e);r!==0&&(t=r,n=Zi(e,r))}if(n===1)throw n=yo,En(e,0),Gt(e,t),He(e,pe()),n;if(n===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,yn(e,Ue,Rt),He(e,pe()),null}function Za(e,t){var n=W;W|=1;try{return e(t)}finally{W=n,W===0&&(xr=pe()+500,el&&fn())}}function bn(e){Yt!==null&&Yt.tag===0&&!(W&6)&&lr();var t=W;W|=1;var n=lt.transition,r=Y;try{if(lt.transition=null,Y=1,e)return e()}finally{Y=r,lt.transition=n,W=t,!(W&6)&&fn()}}function Ja(){Xe=Zn.current,oe(Zn)}function En(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Hg(n)),me!==null)for(n=me.return;n!==null;){var r=n;switch(La(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Rs();break;case 3:gr(),oe(We),oe(Le),Ba();break;case 5:Ua(r);break;case 4:gr();break;case 13:oe(ae);break;case 19:oe(ae);break;case 10:Ma(r.type._context);break;case 22:case 23:Ja()}n=n.return}if(Se=e,me=e=sn(e.current,null),Ne=Xe=t,ge=0,yo=null,Ya=ol=Rn=0,Ue=qr=null,Sn!==null){for(t=0;t<Sn.length;t++)if(n=Sn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var l=s.next;s.next=o,r.next=l}n.pending=r}Sn=null}return e}function xp(e,t){do{var n=me;try{if(Oa(),ds.current=Fs,As){for(var r=ue.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}As=!1}if(_n=0,xe=ve=ue=null,Xr=!1,ho=0,Xa.current=null,n===null||n.return===null){ge=1,yo=t,me=null;break}e:{var s=e,l=n.return,a=n,u=t;if(t=Ne,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,m=a,p=m.tag;if(!(m.mode&1)&&(p===0||p===11||p===15)){var g=m.alternate;g?(m.updateQueue=g.updateQueue,m.memoizedState=g.memoizedState,m.lanes=g.lanes):(m.updateQueue=null,m.memoizedState=null)}var w=xc(l);if(w!==null){w.flags&=-257,wc(w,l,a,s,t),w.mode&1&&yc(s,c,t),t=w,u=c;var j=t.updateQueue;if(j===null){var y=new Set;y.add(u),t.updateQueue=y}else j.add(u);break e}else{if(!(t&1)){yc(s,c,t),eu();break e}u=Error(P(426))}}else if(se&&a.mode&1){var x=xc(l);if(x!==null){!(x.flags&65536)&&(x.flags|=256),wc(x,l,a,s,t),za(yr(u,a));break e}}s=u=yr(u,a),ge!==4&&(ge=2),qr===null?qr=[s]:qr.push(s),s=l;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var h=np(s,u,t);fc(s,h);break e;case 1:a=u;var f=s.type,v=s.stateNode;if(!(s.flags&128)&&(typeof f.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(rn===null||!rn.has(v)))){s.flags|=65536,t&=-t,s.lanes|=t;var S=rp(s,a,t);fc(s,S);break e}}s=s.return}while(s!==null)}jp(n)}catch(E){t=E,me===n&&n!==null&&(me=n=n.return);continue}break}while(!0)}function wp(){var e=$s.current;return $s.current=Fs,e===null?Fs:e}function eu(){(ge===0||ge===3||ge===2)&&(ge=4),Se===null||!(Rn&268435455)&&!(ol&268435455)||Gt(Se,Ne)}function Vs(e,t){var n=W;W|=2;var r=wp();(Se!==e||Ne!==t)&&(Rt=null,En(e,t));do try{gy();break}catch(o){xp(e,o)}while(!0);if(Oa(),W=n,$s.current=r,me!==null)throw Error(P(261));return Se=null,Ne=0,ge}function gy(){for(;me!==null;)Sp(me)}function yy(){for(;me!==null&&!Vv();)Sp(me)}function Sp(e){var t=Cp(e.alternate,e,Xe);e.memoizedProps=e.pendingProps,t===null?jp(e):me=t,Xa.current=null}function jp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=dy(n,t),n!==null){n.flags&=32767,me=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ge=6,me=null;return}}else if(n=cy(n,t,Xe),n!==null){me=n;return}if(t=t.sibling,t!==null){me=t;return}me=t=e}while(t!==null);ge===0&&(ge=5)}function yn(e,t,n){var r=Y,o=lt.transition;try{lt.transition=null,Y=1,xy(e,t,n,r)}finally{lt.transition=o,Y=r}return null}function xy(e,t,n,r){do lr();while(Yt!==null);if(W&6)throw Error(P(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Jv(e,s),e===Se&&(me=Se=null,Ne=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||qo||(qo=!0,Np(Cs,function(){return lr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=lt.transition,lt.transition=null;var l=Y;Y=1;var a=W;W|=4,Xa.current=null,py(e,n),vp(n,e),Fg(Ri),ks=!!_i,Ri=_i=null,e.current=n,my(n),Wv(),W=a,Y=l,lt.transition=s}else e.current=n;if(qo&&(qo=!1,Yt=e,Bs=o),s=e.pendingLanes,s===0&&(rn=null),Kv(n.stateNode),He(e,pe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Us)throw Us=!1,e=Yi,Yi=null,e;return Bs&1&&e.tag!==0&&lr(),s=e.pendingLanes,s&1?e===qi?Zr++:(Zr=0,qi=e):Zr=0,fn(),null}function lr(){if(Yt!==null){var e=tf(Bs),t=lt.transition,n=Y;try{if(lt.transition=null,Y=16>e?16:e,Yt===null)var r=!1;else{if(e=Yt,Yt=null,Bs=0,W&6)throw Error(P(331));var o=W;for(W|=4,I=e.current;I!==null;){var s=I,l=s.child;if(I.flags&16){var a=s.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(I=c;I!==null;){var m=I;switch(m.tag){case 0:case 11:case 15:Yr(8,m,s)}var p=m.child;if(p!==null)p.return=m,I=p;else for(;I!==null;){m=I;var g=m.sibling,w=m.return;if(pp(m),m===c){I=null;break}if(g!==null){g.return=w,I=g;break}I=w}}}var j=s.alternate;if(j!==null){var y=j.child;if(y!==null){j.child=null;do{var x=y.sibling;y.sibling=null,y=x}while(y!==null)}}I=s}}if(s.subtreeFlags&2064&&l!==null)l.return=s,I=l;else e:for(;I!==null;){if(s=I,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Yr(9,s,s.return)}var h=s.sibling;if(h!==null){h.return=s.return,I=h;break e}I=s.return}}var f=e.current;for(I=f;I!==null;){l=I;var v=l.child;if(l.subtreeFlags&2064&&v!==null)v.return=l,I=v;else e:for(l=f;I!==null;){if(a=I,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:rl(9,a)}}catch(E){fe(a,a.return,E)}if(a===l){I=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,I=S;break e}I=a.return}}if(W=o,fn(),Nt&&typeof Nt.onPostCommitFiberRoot=="function")try{Nt.onPostCommitFiberRoot(Xs,e)}catch{}r=!0}return r}finally{Y=n,lt.transition=t}}return!1}function Lc(e,t,n){t=yr(n,t),t=np(e,t,1),e=nn(e,t,1),t=Ae(),e!==null&&(Co(e,1,t),He(e,t))}function fe(e,t,n){if(e.tag===3)Lc(e,e,n);else for(;t!==null;){if(t.tag===3){Lc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(rn===null||!rn.has(r))){e=yr(n,e),e=rp(t,e,1),t=nn(t,e,1),e=Ae(),t!==null&&(Co(t,1,e),He(t,e));break}}t=t.return}}function wy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ae(),e.pingedLanes|=e.suspendedLanes&n,Se===e&&(Ne&n)===n&&(ge===4||ge===3&&(Ne&130023424)===Ne&&500>pe()-qa?En(e,0):Ya|=n),He(e,t)}function Ep(e,t){t===0&&(e.mode&1?(t=Uo,Uo<<=1,!(Uo&130023424)&&(Uo=4194304)):t=1);var n=Ae();e=At(e,t),e!==null&&(Co(e,t,n),He(e,n))}function Sy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ep(e,n)}function jy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(P(314))}r!==null&&r.delete(t),Ep(e,n)}var Cp;Cp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||We.current)Ve=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ve=!1,uy(e,t,n);Ve=!!(e.flags&131072)}else Ve=!1,se&&t.flags&1048576&&Pf(t,Ls,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ps(e,t),e=t.pendingProps;var o=mr(t,Le.current);sr(t,n),o=Wa(null,t,r,e,o,n);var s=Qa();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Qe(r)?(s=!0,bs(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Fa(t),o.updater=nl,t.stateNode=o,o._reactInternals=t,Fi(t,r,e,n),t=Bi(null,t,r,!0,s,n)):(t.tag=0,se&&s&&Ia(t),ze(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ps(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Cy(r),e=dt(r,e),o){case 0:t=Ui(null,t,r,e,n);break e;case 1:t=Ec(null,t,r,e,n);break e;case 11:t=Sc(null,t,r,e,n);break e;case 14:t=jc(null,t,r,dt(r.type,e),n);break e}throw Error(P(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:dt(r,o),Ui(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:dt(r,o),Ec(e,t,r,o,n);case 3:e:{if(ip(t),e===null)throw Error(P(387));r=t.pendingProps,s=t.memoizedState,o=s.element,zf(e,t),Os(t,r,null,n);var l=t.memoizedState;if(r=l.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=yr(Error(P(423)),t),t=Cc(e,t,r,n,o);break e}else if(r!==o){o=yr(Error(P(424)),t),t=Cc(e,t,r,n,o);break e}else for(Ye=tn(t.stateNode.containerInfo.firstChild),qe=t,se=!0,pt=null,n=If(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(hr(),r===o){t=Ft(e,t,n);break e}ze(e,t,r,n)}t=t.child}return t;case 5:return Df(t),e===null&&Oi(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,l=o.children,bi(r,o)?l=null:s!==null&&bi(r,s)&&(t.flags|=32),lp(e,t),ze(e,t,l,n),t.child;case 6:return e===null&&Oi(t),null;case 13:return ap(e,t,n);case 4:return $a(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=vr(t,null,r,n):ze(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:dt(r,o),Sc(e,t,r,o,n);case 7:return ze(e,t,t.pendingProps,n),t.child;case 8:return ze(e,t,t.pendingProps.children,n),t.child;case 12:return ze(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,l=o.value,ee(zs,r._currentValue),r._currentValue=l,s!==null)if(xt(s.value,l)){if(s.children===o.children&&!We.current){t=Ft(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){l=s.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(s.tag===1){u=Dt(-1,n&-n),u.tag=2;var c=s.updateQueue;if(c!==null){c=c.shared;var m=c.pending;m===null?u.next=u:(u.next=m.next,m.next=u),c.pending=u}}s.lanes|=n,u=s.alternate,u!==null&&(u.lanes|=n),Mi(s.return,n,t),a.lanes|=n;break}u=u.next}}else if(s.tag===10)l=s.type===t.type?null:s.child;else if(s.tag===18){if(l=s.return,l===null)throw Error(P(341));l.lanes|=n,a=l.alternate,a!==null&&(a.lanes|=n),Mi(l,n,t),l=s.sibling}else l=s.child;if(l!==null)l.return=s;else for(l=s;l!==null;){if(l===t){l=null;break}if(s=l.sibling,s!==null){s.return=l.return,l=s;break}l=l.return}s=l}ze(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,sr(t,n),o=it(o),r=r(o),t.flags|=1,ze(e,t,r,n),t.child;case 14:return r=t.type,o=dt(r,t.pendingProps),o=dt(r.type,o),jc(e,t,r,o,n);case 15:return op(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:dt(r,o),ps(e,t),t.tag=1,Qe(r)?(e=!0,bs(t)):e=!1,sr(t,n),tp(t,r,o),Fi(t,r,o,n),Bi(null,t,r,!0,e,n);case 19:return up(e,t,n);case 22:return sp(e,t,n)}throw Error(P(156,t.tag))};function Np(e,t){return qd(e,t)}function Ey(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function st(e,t,n,r){return new Ey(e,t,n,r)}function tu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Cy(e){if(typeof e=="function")return tu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===wa)return 11;if(e===Sa)return 14}return 2}function sn(e,t){var n=e.alternate;return n===null?(n=st(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function vs(e,t,n,r,o,s){var l=2;if(r=e,typeof e=="function")tu(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case Bn:return Cn(n.children,o,s,t);case xa:l=8,o|=8;break;case ui:return e=st(12,n,t,o|2),e.elementType=ui,e.lanes=s,e;case ci:return e=st(13,n,t,o),e.elementType=ci,e.lanes=s,e;case di:return e=st(19,n,t,o),e.elementType=di,e.lanes=s,e;case zd:return sl(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Id:l=10;break e;case Ld:l=9;break e;case wa:l=11;break e;case Sa:l=14;break e;case Qt:l=16,r=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=st(l,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function Cn(e,t,n,r){return e=st(7,e,r,t),e.lanes=n,e}function sl(e,t,n,r){return e=st(22,e,r,t),e.elementType=zd,e.lanes=n,e.stateNode={isHidden:!1},e}function Ql(e,t,n){return e=st(6,e,null,t),e.lanes=n,e}function Hl(e,t,n){return t=st(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ny(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=kl(0),this.expirationTimes=kl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=kl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function nu(e,t,n,r,o,s,l,a,u){return e=new Ny(e,t,n,a,u),t===1?(t=1,s===!0&&(t|=8)):t=0,s=st(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fa(s),e}function ky(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Un,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function kp(e){if(!e)return un;e=e._reactInternals;e:{if(zn(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var n=e.type;if(Qe(n))return kf(e,n,t)}return t}function Tp(e,t,n,r,o,s,l,a,u){return e=nu(n,r,!0,e,o,s,l,a,u),e.context=kp(null),n=e.current,r=Ae(),o=on(n),s=Dt(r,o),s.callback=t??null,nn(n,s,o),e.current.lanes=o,Co(e,o,r),He(e,r),e}function ll(e,t,n,r){var o=t.current,s=Ae(),l=on(o);return n=kp(n),t.context===null?t.context=n:t.pendingContext=n,t=Dt(s,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=nn(o,t,l),e!==null&&(yt(e,o,l,s),cs(e,o,l)),l}function Ws(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function zc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ru(e,t){zc(e,t),(e=e.alternate)&&zc(e,t)}function Ty(){return null}var Pp=typeof reportError=="function"?reportError:function(e){console.error(e)};function ou(e){this._internalRoot=e}il.prototype.render=ou.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));ll(e,t,null,null)};il.prototype.unmount=ou.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;bn(function(){ll(null,e,null,null)}),t[Mt]=null}};function il(e){this._internalRoot=e}il.prototype.unstable_scheduleHydration=function(e){if(e){var t=of();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Kt.length&&t!==0&&t<Kt[n].priority;n++);Kt.splice(n,0,e),n===0&&lf(e)}};function su(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function al(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Dc(){}function Py(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var c=Ws(l);s.call(c)}}var l=Tp(t,r,e,0,null,!1,!1,"",Dc);return e._reactRootContainer=l,e[Mt]=l.current,uo(e.nodeType===8?e.parentNode:e),bn(),l}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var c=Ws(u);a.call(c)}}var u=nu(e,0,!1,null,null,!1,!1,"",Dc);return e._reactRootContainer=u,e[Mt]=u.current,uo(e.nodeType===8?e.parentNode:e),bn(function(){ll(t,u,n,r)}),u}function ul(e,t,n,r,o){var s=n._reactRootContainer;if(s){var l=s;if(typeof o=="function"){var a=o;o=function(){var u=Ws(l);a.call(u)}}ll(t,l,e,o)}else l=Py(n,t,e,o,r);return Ws(l)}nf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Br(t.pendingLanes);n!==0&&(Ca(t,n|1),He(t,pe()),!(W&6)&&(xr=pe()+500,fn()))}break;case 13:bn(function(){var r=At(e,1);if(r!==null){var o=Ae();yt(r,e,1,o)}}),ru(e,1)}};Na=function(e){if(e.tag===13){var t=At(e,134217728);if(t!==null){var n=Ae();yt(t,e,134217728,n)}ru(e,134217728)}};rf=function(e){if(e.tag===13){var t=on(e),n=At(e,t);if(n!==null){var r=Ae();yt(n,e,t,r)}ru(e,t)}};of=function(){return Y};sf=function(e,t){var n=Y;try{return Y=e,t()}finally{Y=n}};Si=function(e,t,n){switch(t){case"input":if(mi(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Js(r);if(!o)throw Error(P(90));Od(r),mi(r,o)}}}break;case"textarea":Ad(e,n);break;case"select":t=n.value,t!=null&&tr(e,!!n.multiple,t,!1)}};Qd=Za;Hd=bn;var _y={usingClientEntryPoint:!1,Events:[ko,Hn,Js,Vd,Wd,Za]},Mr={findFiberByHostInstance:wn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ry={bundleType:Mr.bundleType,version:Mr.version,rendererPackageName:Mr.rendererPackageName,rendererConfig:Mr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:$t.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Xd(e),e===null?null:e.stateNode},findFiberByHostInstance:Mr.findFiberByHostInstance||Ty,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Zo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Zo.isDisabled&&Zo.supportsFiber)try{Xs=Zo.inject(Ry),Nt=Zo}catch{}}Je.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=_y;Je.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!su(t))throw Error(P(200));return ky(e,t,null,n)};Je.createRoot=function(e,t){if(!su(e))throw Error(P(299));var n=!1,r="",o=Pp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=nu(e,1,!1,null,null,n,!1,r,o),e[Mt]=t.current,uo(e.nodeType===8?e.parentNode:e),new ou(t)};Je.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=Xd(t),e=e===null?null:e.stateNode,e};Je.flushSync=function(e){return bn(e)};Je.hydrate=function(e,t,n){if(!al(t))throw Error(P(200));return ul(null,e,t,!0,n)};Je.hydrateRoot=function(e,t,n){if(!su(e))throw Error(P(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",l=Pp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=Tp(t,null,e,1,n??null,o,!1,s,l),e[Mt]=t.current,uo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new il(t)};Je.render=function(e,t,n){if(!al(t))throw Error(P(200));return ul(null,e,t,!1,n)};Je.unmountComponentAtNode=function(e){if(!al(e))throw Error(P(40));return e._reactRootContainer?(bn(function(){ul(null,null,e,!1,function(){e._reactRootContainer=null,e[Mt]=null})}),!0):!1};Je.unstable_batchedUpdates=Za;Je.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!al(n))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return ul(e,t,n,!1,r)};Je.version="18.3.1-next-f1338f8080-20240426";function _p(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(_p)}catch(e){console.error(e)}}_p(),Pd.exports=Je;var cl=Pd.exports;const by=vd(cl);var Rp,Oc=cl;Rp=Oc.createRoot,Oc.hydrateRoot;const Iy=e=>e instanceof Error?e.message+`
`+e.stack:JSON.stringify(e,null,2);class bp extends _t.Component{constructor(t){super(t),this.state={hasError:!1,error:null}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}render(){return this.state.hasError?i.jsxs("div",{className:"p-4 border border-red-500 rounded",children:[i.jsx("h2",{className:"text-red-500",children:"Something went wrong."}),i.jsx("pre",{className:"mt-2 text-sm",children:Iy(this.state.error)})]}):this.props.children}}/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function xo(){return xo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},xo.apply(this,arguments)}var qt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(qt||(qt={}));const Mc="popstate";function Ly(e){e===void 0&&(e={});function t(r,o){let{pathname:s,search:l,hash:a}=r.location;return ea("",{pathname:s,search:l,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Qs(o)}return Dy(t,n,null,e)}function he(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Ip(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function zy(){return Math.random().toString(36).substr(2,8)}function Ac(e,t){return{usr:e.state,key:e.key,idx:t}}function ea(e,t,n,r){return n===void 0&&(n=null),xo({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Cr(t):t,{state:n,key:t&&t.key||r||zy()})}function Qs(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Cr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Dy(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:s=!1}=r,l=o.history,a=qt.Pop,u=null,c=m();c==null&&(c=0,l.replaceState(xo({},l.state,{idx:c}),""));function m(){return(l.state||{idx:null}).idx}function p(){a=qt.Pop;let x=m(),h=x==null?null:x-c;c=x,u&&u({action:a,location:y.location,delta:h})}function g(x,h){a=qt.Push;let f=ea(y.location,x,h);c=m()+1;let v=Ac(f,c),S=y.createHref(f);try{l.pushState(v,"",S)}catch(E){if(E instanceof DOMException&&E.name==="DataCloneError")throw E;o.location.assign(S)}s&&u&&u({action:a,location:y.location,delta:1})}function w(x,h){a=qt.Replace;let f=ea(y.location,x,h);c=m();let v=Ac(f,c),S=y.createHref(f);l.replaceState(v,"",S),s&&u&&u({action:a,location:y.location,delta:0})}function j(x){let h=o.location.origin!=="null"?o.location.origin:o.location.href,f=typeof x=="string"?x:Qs(x);return f=f.replace(/ $/,"%20"),he(h,"No window.location.(origin|href) available to create URL for href: "+f),new URL(f,h)}let y={get action(){return a},get location(){return e(o,l)},listen(x){if(u)throw new Error("A history only accepts one active listener");return o.addEventListener(Mc,p),u=x,()=>{o.removeEventListener(Mc,p),u=null}},createHref(x){return t(o,x)},createURL:j,encodeLocation(x){let h=j(x);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:g,replace:w,go(x){return l.go(x)}};return y}var Fc;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Fc||(Fc={}));function Oy(e,t,n){return n===void 0&&(n="/"),My(e,t,n)}function My(e,t,n,r){let o=typeof t=="string"?Cr(t):t,s=lu(o.pathname||"/",n);if(s==null)return null;let l=Lp(e);Ay(l);let a=null;for(let u=0;a==null&&u<l.length;++u){let c=Yy(s);a=Ky(l[u],c)}return a}function Lp(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(s,l,a)=>{let u={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:l,route:s};u.relativePath.startsWith("/")&&(he(u.relativePath.startsWith(r),'Absolute route path "'+u.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),u.relativePath=u.relativePath.slice(r.length));let c=ln([r,u.relativePath]),m=n.concat(u);s.children&&s.children.length>0&&(he(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),Lp(s.children,t,m,c)),!(s.path==null&&!s.index)&&t.push({path:c,score:Qy(c,s.index),routesMeta:m})};return e.forEach((s,l)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))o(s,l);else for(let u of zp(s.path))o(s,l,u)}),t}function zp(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return o?[s,""]:[s];let l=zp(r.join("/")),a=[];return a.push(...l.map(u=>u===""?s:[s,u].join("/"))),o&&a.push(...l),a.map(u=>e.startsWith("/")&&u===""?"/":u)}function Ay(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Hy(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Fy=/^:[\w-]+$/,$y=3,Uy=2,By=1,Vy=10,Wy=-2,$c=e=>e==="*";function Qy(e,t){let n=e.split("/"),r=n.length;return n.some($c)&&(r+=Wy),t&&(r+=Uy),n.filter(o=>!$c(o)).reduce((o,s)=>o+(Fy.test(s)?$y:s===""?By:Vy),r)}function Hy(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function Ky(e,t,n){let{routesMeta:r}=e,o={},s="/",l=[];for(let a=0;a<r.length;++a){let u=r[a],c=a===r.length-1,m=s==="/"?t:t.slice(s.length)||"/",p=Gy({path:u.relativePath,caseSensitive:u.caseSensitive,end:c},m),g=u.route;if(!p)return null;Object.assign(o,p.params),l.push({params:o,pathname:ln([s,p.pathname]),pathnameBase:ex(ln([s,p.pathnameBase])),route:g}),p.pathnameBase!=="/"&&(s=ln([s,p.pathnameBase]))}return l}function Gy(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Xy(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let s=o[0],l=s.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((c,m,p)=>{let{paramName:g,isOptional:w}=m;if(g==="*"){let y=a[p]||"";l=s.slice(0,s.length-y.length).replace(/(.)\/+$/,"$1")}const j=a[p];return w&&!j?c[g]=void 0:c[g]=(j||"").replace(/%2F/g,"/"),c},{}),pathname:s,pathnameBase:l,pattern:e}}function Xy(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Ip(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,a,u)=>(r.push({paramName:a,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function Yy(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Ip(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function lu(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function qy(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?Cr(e):e;return{pathname:n?n.startsWith("/")?n:Zy(n,t):t,search:tx(r),hash:nx(o)}}function Zy(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function Kl(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Jy(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Dp(e,t){let n=Jy(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Op(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=Cr(e):(o=xo({},e),he(!o.pathname||!o.pathname.includes("?"),Kl("?","pathname","search",o)),he(!o.pathname||!o.pathname.includes("#"),Kl("#","pathname","hash",o)),he(!o.search||!o.search.includes("#"),Kl("#","search","hash",o)));let s=e===""||o.pathname==="",l=s?"/":o.pathname,a;if(l==null)a=n;else{let p=t.length-1;if(!r&&l.startsWith("..")){let g=l.split("/");for(;g[0]==="..";)g.shift(),p-=1;o.pathname=g.join("/")}a=p>=0?t[p]:"/"}let u=qy(o,a),c=l&&l!=="/"&&l.endsWith("/"),m=(s||l===".")&&n.endsWith("/");return!u.pathname.endsWith("/")&&(c||m)&&(u.pathname+="/"),u}const ln=e=>e.join("/").replace(/\/\/+/g,"/"),ex=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),tx=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,nx=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function rx(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Mp=["post","put","patch","delete"];new Set(Mp);const ox=["get",...Mp];new Set(ox);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function wo(){return wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},wo.apply(this,arguments)}const iu=d.createContext(null),sx=d.createContext(null),Dn=d.createContext(null),dl=d.createContext(null),pn=d.createContext({outlet:null,matches:[],isDataRoute:!1}),Ap=d.createContext(null);function lx(e,t){let{relative:n}=t===void 0?{}:t;Po()||he(!1);let{basename:r,navigator:o}=d.useContext(Dn),{hash:s,pathname:l,search:a}=Up(e,{relative:n}),u=l;return r!=="/"&&(u=l==="/"?r:ln([r,l])),o.createHref({pathname:u,search:a,hash:s})}function Po(){return d.useContext(dl)!=null}function On(){return Po()||he(!1),d.useContext(dl).location}function Fp(e){d.useContext(Dn).static||d.useLayoutEffect(e)}function $p(){let{isDataRoute:e}=d.useContext(pn);return e?Sx():ix()}function ix(){Po()||he(!1);let e=d.useContext(iu),{basename:t,future:n,navigator:r}=d.useContext(Dn),{matches:o}=d.useContext(pn),{pathname:s}=On(),l=JSON.stringify(Dp(o,n.v7_relativeSplatPath)),a=d.useRef(!1);return Fp(()=>{a.current=!0}),d.useCallback(function(c,m){if(m===void 0&&(m={}),!a.current)return;if(typeof c=="number"){r.go(c);return}let p=Op(c,JSON.parse(l),s,m.relative==="path");e==null&&t!=="/"&&(p.pathname=p.pathname==="/"?t:ln([t,p.pathname])),(m.replace?r.replace:r.push)(p,m.state,m)},[t,r,l,s,e])}const ax=d.createContext(null);function ux(e){let t=d.useContext(pn).outlet;return t&&d.createElement(ax.Provider,{value:e},t)}function Up(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=d.useContext(Dn),{matches:o}=d.useContext(pn),{pathname:s}=On(),l=JSON.stringify(Dp(o,r.v7_relativeSplatPath));return d.useMemo(()=>Op(e,JSON.parse(l),s,n==="path"),[e,l,s,n])}function cx(e,t){return dx(e,t)}function dx(e,t,n,r){Po()||he(!1);let{navigator:o}=d.useContext(Dn),{matches:s}=d.useContext(pn),l=s[s.length-1],a=l?l.params:{};l&&l.pathname;let u=l?l.pathnameBase:"/";l&&l.route;let c=On(),m;if(t){var p;let x=typeof t=="string"?Cr(t):t;u==="/"||(p=x.pathname)!=null&&p.startsWith(u)||he(!1),m=x}else m=c;let g=m.pathname||"/",w=g;if(u!=="/"){let x=u.replace(/^\//,"").split("/");w="/"+g.replace(/^\//,"").split("/").slice(x.length).join("/")}let j=Oy(e,{pathname:w}),y=vx(j&&j.map(x=>Object.assign({},x,{params:Object.assign({},a,x.params),pathname:ln([u,o.encodeLocation?o.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?u:ln([u,o.encodeLocation?o.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),s,n,r);return t&&y?d.createElement(dl.Provider,{value:{location:wo({pathname:"/",search:"",hash:"",state:null,key:"default"},m),navigationType:qt.Pop}},y):y}function fx(){let e=wx(),t=rx(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return d.createElement(d.Fragment,null,d.createElement("h2",null,"Unexpected Application Error!"),d.createElement("h3",{style:{fontStyle:"italic"}},t),n?d.createElement("pre",{style:o},n):null,null)}const px=d.createElement(fx,null);class mx extends d.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?d.createElement(pn.Provider,{value:this.props.routeContext},d.createElement(Ap.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function hx(e){let{routeContext:t,match:n,children:r}=e,o=d.useContext(iu);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),d.createElement(pn.Provider,{value:t},r)}function vx(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let l=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let m=l.findIndex(p=>p.route.id&&(a==null?void 0:a[p.route.id])!==void 0);m>=0||he(!1),l=l.slice(0,Math.min(l.length,m+1))}let u=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let m=0;m<l.length;m++){let p=l[m];if((p.route.HydrateFallback||p.route.hydrateFallbackElement)&&(c=m),p.route.id){let{loaderData:g,errors:w}=n,j=p.route.loader&&g[p.route.id]===void 0&&(!w||w[p.route.id]===void 0);if(p.route.lazy||j){u=!0,c>=0?l=l.slice(0,c+1):l=[l[0]];break}}}return l.reduceRight((m,p,g)=>{let w,j=!1,y=null,x=null;n&&(w=a&&p.route.id?a[p.route.id]:void 0,y=p.route.errorElement||px,u&&(c<0&&g===0?(jx("route-fallback"),j=!0,x=null):c===g&&(j=!0,x=p.route.hydrateFallbackElement||null)));let h=t.concat(l.slice(0,g+1)),f=()=>{let v;return w?v=y:j?v=x:p.route.Component?v=d.createElement(p.route.Component,null):p.route.element?v=p.route.element:v=m,d.createElement(hx,{match:p,routeContext:{outlet:m,matches:h,isDataRoute:n!=null},children:v})};return n&&(p.route.ErrorBoundary||p.route.errorElement||g===0)?d.createElement(mx,{location:n.location,revalidation:n.revalidation,component:y,error:w,children:f(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):f()},null)}var Bp=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Bp||{}),Vp=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Vp||{});function gx(e){let t=d.useContext(iu);return t||he(!1),t}function yx(e){let t=d.useContext(sx);return t||he(!1),t}function xx(e){let t=d.useContext(pn);return t||he(!1),t}function Wp(e){let t=xx(),n=t.matches[t.matches.length-1];return n.route.id||he(!1),n.route.id}function wx(){var e;let t=d.useContext(Ap),n=yx(),r=Wp();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Sx(){let{router:e}=gx(Bp.UseNavigateStable),t=Wp(Vp.UseNavigateStable),n=d.useRef(!1);return Fp(()=>{n.current=!0}),d.useCallback(function(o,s){s===void 0&&(s={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,wo({fromRouteId:t},s)))},[e,t])}const Uc={};function jx(e,t,n){Uc[e]||(Uc[e]=!0)}function Ex(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function Cx(e){return ux(e.context)}function xn(e){he(!1)}function Nx(e){let{basename:t="/",children:n=null,location:r,navigationType:o=qt.Pop,navigator:s,static:l=!1,future:a}=e;Po()&&he(!1);let u=t.replace(/^\/*/,"/"),c=d.useMemo(()=>({basename:u,navigator:s,static:l,future:wo({v7_relativeSplatPath:!1},a)}),[u,a,s,l]);typeof r=="string"&&(r=Cr(r));let{pathname:m="/",search:p="",hash:g="",state:w=null,key:j="default"}=r,y=d.useMemo(()=>{let x=lu(m,u);return x==null?null:{location:{pathname:x,search:p,hash:g,state:w,key:j},navigationType:o}},[u,m,p,g,w,j,o]);return y==null?null:d.createElement(Dn.Provider,{value:c},d.createElement(dl.Provider,{children:n,value:y}))}function kx(e){let{children:t,location:n}=e;return cx(ta(t),n)}new Promise(()=>{});function ta(e,t){t===void 0&&(t=[]);let n=[];return d.Children.forEach(e,(r,o)=>{if(!d.isValidElement(r))return;let s=[...t,o];if(r.type===d.Fragment){n.push.apply(n,ta(r.props.children,s));return}r.type!==xn&&he(!1),!r.props.index||!r.props.children||he(!1);let l={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=ta(r.props.children,s)),n.push(l)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function na(){return na=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},na.apply(this,arguments)}function Tx(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,s;for(s=0;s<r.length;s++)o=r[s],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Px(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function _x(e,t){return e.button===0&&(!t||t==="_self")&&!Px(e)}const Rx=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],bx="6";try{window.__reactRouterVersion=bx}catch{}const Ix="startTransition",Bc=ha[Ix];function Lx(e){let{basename:t,children:n,future:r,window:o}=e,s=d.useRef();s.current==null&&(s.current=Ly({window:o,v5Compat:!0}));let l=s.current,[a,u]=d.useState({action:l.action,location:l.location}),{v7_startTransition:c}=r||{},m=d.useCallback(p=>{c&&Bc?Bc(()=>u(p)):u(p)},[u,c]);return d.useLayoutEffect(()=>l.listen(m),[l,m]),d.useEffect(()=>Ex(r),[r]),d.createElement(Nx,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:l,future:r})}const zx=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Dx=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Gl=d.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:s,replace:l,state:a,target:u,to:c,preventScrollReset:m,viewTransition:p}=t,g=Tx(t,Rx),{basename:w}=d.useContext(Dn),j,y=!1;if(typeof c=="string"&&Dx.test(c)&&(j=c,zx))try{let v=new URL(window.location.href),S=c.startsWith("//")?new URL(v.protocol+c):new URL(c),E=lu(S.pathname,w);S.origin===v.origin&&E!=null?c=E+S.search+S.hash:y=!0}catch{}let x=lx(c,{relative:o}),h=Ox(c,{replace:l,state:a,target:u,preventScrollReset:m,relative:o,viewTransition:p});function f(v){r&&r(v),v.defaultPrevented||h(v)}return d.createElement("a",na({},g,{href:j||x,onClick:y||s?r:f,ref:n,target:u}))});var Vc;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Vc||(Vc={}));var Wc;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Wc||(Wc={}));function Ox(e,t){let{target:n,replace:r,state:o,preventScrollReset:s,relative:l,viewTransition:a}=t===void 0?{}:t,u=$p(),c=On(),m=Up(e,{relative:l});return d.useCallback(p=>{if(_x(p,n)){p.preventDefault();let g=r!==void 0?r:Qs(c)===Qs(m);u(e,{replace:g,state:o,preventScrollReset:s,relative:l,viewTransition:a})}},[c,u,m,r,o,n,e,s,l,a])}const Mx=1,Ax=1e6;let Xl=0;function Fx(){return Xl=(Xl+1)%Number.MAX_SAFE_INTEGER,Xl.toString()}const Yl=new Map,Qc=e=>{if(Yl.has(e))return;const t=setTimeout(()=>{Yl.delete(e),Jr({type:"REMOVE_TOAST",toastId:e})},Ax);Yl.set(e,t)},$x=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Mx)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?Qc(n):e.toasts.forEach(r=>{Qc(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},gs=[];let ys={toasts:[]};function Jr(e){ys=$x(ys,e),gs.forEach(t=>{t(ys)})}function Ux({...e}){const t=Fx(),n=o=>Jr({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>Jr({type:"DISMISS_TOAST",toastId:t});return Jr({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function fl(){const[e,t]=d.useState(ys);return d.useEffect(()=>(gs.push(t),()=>{const n=gs.indexOf(t);n>-1&&gs.splice(n,1)}),[e]),{...e,toast:Ux,dismiss:n=>Jr({type:"DISMISS_TOAST",toastId:n})}}function J(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Hc(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Qp(...e){return t=>{let n=!1;const r=e.map(o=>{const s=Hc(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():Hc(e[o],null)}}}}function ut(...e){return d.useCallback(Qp(...e),e)}function Bx(e,t){const n=d.createContext(t),r=s=>{const{children:l,...a}=s,u=d.useMemo(()=>a,Object.values(a));return i.jsx(n.Provider,{value:u,children:l})};r.displayName=e+"Provider";function o(s){const l=d.useContext(n);if(l)return l;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[r,o]}function _o(e,t=[]){let n=[];function r(s,l){const a=d.createContext(l),u=n.length;n=[...n,l];const c=p=>{var h;const{scope:g,children:w,...j}=p,y=((h=g==null?void 0:g[e])==null?void 0:h[u])||a,x=d.useMemo(()=>j,Object.values(j));return i.jsx(y.Provider,{value:x,children:w})};c.displayName=s+"Provider";function m(p,g){var y;const w=((y=g==null?void 0:g[e])==null?void 0:y[u])||a,j=d.useContext(w);if(j)return j;if(l!==void 0)return l;throw new Error(`\`${p}\` must be used within \`${s}\``)}return[c,m]}const o=()=>{const s=n.map(l=>d.createContext(l));return function(a){const u=(a==null?void 0:a[e])||s;return d.useMemo(()=>({[`__scope${e}`]:{...a,[e]:u}}),[a,u])}};return o.scopeName=e,[r,Vx(o,...t)]}function Vx(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const l=r.reduce((a,{useScope:u,scopeName:c})=>{const p=u(s)[`__scope${c}`];return{...a,...p}},{});return d.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}function So(e){const t=Qx(e),n=d.forwardRef((r,o)=>{const{children:s,...l}=r,a=d.Children.toArray(s),u=a.find(Kx);if(u){const c=u.props.children,m=a.map(p=>p===u?d.Children.count(c)>1?d.Children.only(null):d.isValidElement(c)?c.props.children:null:p);return i.jsx(t,{...l,ref:o,children:d.isValidElement(c)?d.cloneElement(c,void 0,m):null})}return i.jsx(t,{...l,ref:o,children:s})});return n.displayName=`${e}.Slot`,n}var Wx=So("Slot");function Qx(e){const t=d.forwardRef((n,r)=>{const{children:o,...s}=n;if(d.isValidElement(o)){const l=Xx(o),a=Gx(s,o.props);return o.type!==d.Fragment&&(a.ref=r?Qp(r,l):l),d.cloneElement(o,a)}return d.Children.count(o)>1?d.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Hx=Symbol("radix.slottable");function Kx(e){return d.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Hx}function Gx(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...a)=>{const u=s(...a);return o(...a),u}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function Xx(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Hp(e){const t=e+"CollectionProvider",[n,r]=_o(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=y=>{const{scope:x,children:h}=y,f=_t.useRef(null),v=_t.useRef(new Map).current;return i.jsx(o,{scope:x,itemMap:v,collectionRef:f,children:h})};l.displayName=t;const a=e+"CollectionSlot",u=So(a),c=_t.forwardRef((y,x)=>{const{scope:h,children:f}=y,v=s(a,h),S=ut(x,v.collectionRef);return i.jsx(u,{ref:S,children:f})});c.displayName=a;const m=e+"CollectionItemSlot",p="data-radix-collection-item",g=So(m),w=_t.forwardRef((y,x)=>{const{scope:h,children:f,...v}=y,S=_t.useRef(null),E=ut(x,S),T=s(m,h);return _t.useEffect(()=>(T.itemMap.set(S,{ref:S,...v}),()=>void T.itemMap.delete(S))),i.jsx(g,{[p]:"",ref:E,children:f})});w.displayName=m;function j(y){const x=s(e+"CollectionConsumer",y);return _t.useCallback(()=>{const f=x.collectionRef.current;if(!f)return[];const v=Array.from(f.querySelectorAll(`[${p}]`));return Array.from(x.itemMap.values()).sort((T,C)=>v.indexOf(T.ref.current)-v.indexOf(C.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:l,Slot:c,ItemSlot:w},j,r]}var Yx=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],le=Yx.reduce((e,t)=>{const n=So(`Primitive.${t}`),r=d.forwardRef((o,s)=>{const{asChild:l,...a}=o,u=l?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),i.jsx(u,{...a,ref:s})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Kp(e,t){e&&cl.flushSync(()=>e.dispatchEvent(t))}function Tt(e){const t=d.useRef(e);return d.useEffect(()=>{t.current=e}),d.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function qx(e,t=globalThis==null?void 0:globalThis.document){const n=Tt(e);d.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Zx="DismissableLayer",ra="dismissableLayer.update",Jx="dismissableLayer.pointerDownOutside",e0="dismissableLayer.focusOutside",Kc,Gp=d.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),au=d.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:l,onDismiss:a,...u}=e,c=d.useContext(Gp),[m,p]=d.useState(null),g=(m==null?void 0:m.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,w]=d.useState({}),j=ut(t,C=>p(C)),y=Array.from(c.layers),[x]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),h=y.indexOf(x),f=m?y.indexOf(m):-1,v=c.layersWithOutsidePointerEventsDisabled.size>0,S=f>=h,E=n0(C=>{const _=C.target,z=[...c.branches].some(O=>O.contains(_));!S||z||(o==null||o(C),l==null||l(C),C.defaultPrevented||a==null||a())},g),T=r0(C=>{const _=C.target;[...c.branches].some(O=>O.contains(_))||(s==null||s(C),l==null||l(C),C.defaultPrevented||a==null||a())},g);return qx(C=>{f===c.layers.size-1&&(r==null||r(C),!C.defaultPrevented&&a&&(C.preventDefault(),a()))},g),d.useEffect(()=>{if(m)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(Kc=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(m)),c.layers.add(m),Gc(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=Kc)}},[m,g,n,c]),d.useEffect(()=>()=>{m&&(c.layers.delete(m),c.layersWithOutsidePointerEventsDisabled.delete(m),Gc())},[m,c]),d.useEffect(()=>{const C=()=>w({});return document.addEventListener(ra,C),()=>document.removeEventListener(ra,C)},[]),i.jsx(le.div,{...u,ref:j,style:{pointerEvents:v?S?"auto":"none":void 0,...e.style},onFocusCapture:J(e.onFocusCapture,T.onFocusCapture),onBlurCapture:J(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:J(e.onPointerDownCapture,E.onPointerDownCapture)})});au.displayName=Zx;var t0="DismissableLayerBranch",Xp=d.forwardRef((e,t)=>{const n=d.useContext(Gp),r=d.useRef(null),o=ut(t,r);return d.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),i.jsx(le.div,{...e,ref:o})});Xp.displayName=t0;function n0(e,t=globalThis==null?void 0:globalThis.document){const n=Tt(e),r=d.useRef(!1),o=d.useRef(()=>{});return d.useEffect(()=>{const s=a=>{if(a.target&&!r.current){let u=function(){Yp(Jx,n,c,{discrete:!0})};const c={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},l=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(l),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function r0(e,t=globalThis==null?void 0:globalThis.document){const n=Tt(e),r=d.useRef(!1);return d.useEffect(()=>{const o=s=>{s.target&&!r.current&&Yp(e0,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Gc(){const e=new CustomEvent(ra);document.dispatchEvent(e)}function Yp(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Kp(o,s):o.dispatchEvent(s)}var o0=au,s0=Xp,wr=globalThis!=null&&globalThis.document?d.useLayoutEffect:()=>{},l0="Portal",uu=d.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,s]=d.useState(!1);wr(()=>s(!0),[]);const l=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return l?by.createPortal(i.jsx(le.div,{...r,ref:t}),l):null});uu.displayName=l0;function i0(e,t){return d.useReducer((n,r)=>t[n][r]??n,e)}var Nr=e=>{const{present:t,children:n}=e,r=a0(t),o=typeof n=="function"?n({present:r.isPresent}):d.Children.only(n),s=ut(r.ref,u0(o));return typeof n=="function"||r.isPresent?d.cloneElement(o,{ref:s}):null};Nr.displayName="Presence";function a0(e){const[t,n]=d.useState(),r=d.useRef(null),o=d.useRef(e),s=d.useRef("none"),l=e?"mounted":"unmounted",[a,u]=i0(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return d.useEffect(()=>{const c=Jo(r.current);s.current=a==="mounted"?c:"none"},[a]),wr(()=>{const c=r.current,m=o.current;if(m!==e){const g=s.current,w=Jo(c);e?u("MOUNT"):w==="none"||(c==null?void 0:c.display)==="none"?u("UNMOUNT"):u(m&&g!==w?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,u]),wr(()=>{if(t){let c;const m=t.ownerDocument.defaultView??window,p=w=>{const y=Jo(r.current).includes(w.animationName);if(w.target===t&&y&&(u("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",c=m.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},g=w=>{w.target===t&&(s.current=Jo(r.current))};return t.addEventListener("animationstart",g),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{m.clearTimeout(c),t.removeEventListener("animationstart",g),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:d.useCallback(c=>{r.current=c?getComputedStyle(c):null,n(c)},[])}}function Jo(e){return(e==null?void 0:e.animationName)||"none"}function u0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var c0=ha[" useInsertionEffect ".trim().toString()]||wr;function pl({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,s,l]=d0({defaultProp:t,onChange:n}),a=e!==void 0,u=a?e:o;{const m=d.useRef(e!==void 0);d.useEffect(()=>{const p=m.current;p!==a&&console.warn(`${r} is changing from ${p?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),m.current=a},[a,r])}const c=d.useCallback(m=>{var p;if(a){const g=f0(m)?m(e):m;g!==e&&((p=l.current)==null||p.call(l,g))}else s(m)},[a,e,s,l]);return[u,c]}function d0({defaultProp:e,onChange:t}){const[n,r]=d.useState(e),o=d.useRef(n),s=d.useRef(t);return c0(()=>{s.current=t},[t]),d.useEffect(()=>{var l;o.current!==n&&((l=s.current)==null||l.call(s,n),o.current=n)},[n,o]),[n,r,s]}function f0(e){return typeof e=="function"}var p0=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),m0="VisuallyHidden",cu=d.forwardRef((e,t)=>i.jsx(le.span,{...e,ref:t,style:{...p0,...e.style}}));cu.displayName=m0;var du="ToastProvider",[fu,h0,v0]=Hp("Toast"),[qp,mS]=_o("Toast",[v0]),[g0,ml]=qp(du),Zp=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:l}=e,[a,u]=d.useState(null),[c,m]=d.useState(0),p=d.useRef(!1),g=d.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${du}\`. Expected non-empty \`string\`.`),i.jsx(fu.Provider,{scope:t,children:i.jsx(g0,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:s,toastCount:c,viewport:a,onViewportChange:u,onToastAdd:d.useCallback(()=>m(w=>w+1),[]),onToastRemove:d.useCallback(()=>m(w=>w-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:g,children:l})})};Zp.displayName=du;var Jp="ToastViewport",y0=["F8"],oa="toast.viewportPause",sa="toast.viewportResume",em=d.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=y0,label:o="Notifications ({hotkey})",...s}=e,l=ml(Jp,n),a=h0(n),u=d.useRef(null),c=d.useRef(null),m=d.useRef(null),p=d.useRef(null),g=ut(t,p,l.onViewportChange),w=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),j=l.toastCount>0;d.useEffect(()=>{const x=h=>{var v;r.length!==0&&r.every(S=>h[S]||h.code===S)&&((v=p.current)==null||v.focus())};return document.addEventListener("keydown",x),()=>document.removeEventListener("keydown",x)},[r]),d.useEffect(()=>{const x=u.current,h=p.current;if(j&&x&&h){const f=()=>{if(!l.isClosePausedRef.current){const T=new CustomEvent(oa);h.dispatchEvent(T),l.isClosePausedRef.current=!0}},v=()=>{if(l.isClosePausedRef.current){const T=new CustomEvent(sa);h.dispatchEvent(T),l.isClosePausedRef.current=!1}},S=T=>{!x.contains(T.relatedTarget)&&v()},E=()=>{x.contains(document.activeElement)||v()};return x.addEventListener("focusin",f),x.addEventListener("focusout",S),x.addEventListener("pointermove",f),x.addEventListener("pointerleave",E),window.addEventListener("blur",f),window.addEventListener("focus",v),()=>{x.removeEventListener("focusin",f),x.removeEventListener("focusout",S),x.removeEventListener("pointermove",f),x.removeEventListener("pointerleave",E),window.removeEventListener("blur",f),window.removeEventListener("focus",v)}}},[j,l.isClosePausedRef]);const y=d.useCallback(({tabbingDirection:x})=>{const f=a().map(v=>{const S=v.ref.current,E=[S,...b0(S)];return x==="forwards"?E:E.reverse()});return(x==="forwards"?f.reverse():f).flat()},[a]);return d.useEffect(()=>{const x=p.current;if(x){const h=f=>{var E,T,C;const v=f.altKey||f.ctrlKey||f.metaKey;if(f.key==="Tab"&&!v){const _=document.activeElement,z=f.shiftKey;if(f.target===x&&z){(E=c.current)==null||E.focus();return}const F=y({tabbingDirection:z?"backwards":"forwards"}),q=F.findIndex(A=>A===_);ql(F.slice(q+1))?f.preventDefault():z?(T=c.current)==null||T.focus():(C=m.current)==null||C.focus()}};return x.addEventListener("keydown",h),()=>x.removeEventListener("keydown",h)}},[a,y]),i.jsxs(s0,{ref:u,role:"region","aria-label":o.replace("{hotkey}",w),tabIndex:-1,style:{pointerEvents:j?void 0:"none"},children:[j&&i.jsx(la,{ref:c,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"forwards"});ql(x)}}),i.jsx(fu.Slot,{scope:n,children:i.jsx(le.ol,{tabIndex:-1,...s,ref:g})}),j&&i.jsx(la,{ref:m,onFocusFromOutsideViewport:()=>{const x=y({tabbingDirection:"backwards"});ql(x)}})]})});em.displayName=Jp;var tm="ToastFocusProxy",la=d.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,s=ml(tm,n);return i.jsx(cu,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:l=>{var c;const a=l.relatedTarget;!((c=s.viewport)!=null&&c.contains(a))&&r()}})});la.displayName=tm;var Ro="Toast",x0="toast.swipeStart",w0="toast.swipeMove",S0="toast.swipeCancel",j0="toast.swipeEnd",nm=d.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:s,...l}=e,[a,u]=pl({prop:r,defaultProp:o??!0,onChange:s,caller:Ro});return i.jsx(Nr,{present:n||a,children:i.jsx(N0,{open:a,...l,ref:t,onClose:()=>u(!1),onPause:Tt(e.onPause),onResume:Tt(e.onResume),onSwipeStart:J(e.onSwipeStart,c=>{c.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:J(e.onSwipeMove,c=>{const{x:m,y:p}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","move"),c.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${m}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${p}px`)}),onSwipeCancel:J(e.onSwipeCancel,c=>{c.currentTarget.setAttribute("data-swipe","cancel"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:J(e.onSwipeEnd,c=>{const{x:m,y:p}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","end"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${m}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${p}px`),u(!1)})})})});nm.displayName=Ro;var[E0,C0]=qp(Ro,{onClose(){}}),N0=d.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:s,onClose:l,onEscapeKeyDown:a,onPause:u,onResume:c,onSwipeStart:m,onSwipeMove:p,onSwipeCancel:g,onSwipeEnd:w,...j}=e,y=ml(Ro,n),[x,h]=d.useState(null),f=ut(t,A=>h(A)),v=d.useRef(null),S=d.useRef(null),E=o||y.duration,T=d.useRef(0),C=d.useRef(E),_=d.useRef(0),{onToastAdd:z,onToastRemove:O}=y,Q=Tt(()=>{var te;(x==null?void 0:x.contains(document.activeElement))&&((te=y.viewport)==null||te.focus()),l()}),F=d.useCallback(A=>{!A||A===1/0||(window.clearTimeout(_.current),T.current=new Date().getTime(),_.current=window.setTimeout(Q,A))},[Q]);d.useEffect(()=>{const A=y.viewport;if(A){const te=()=>{F(C.current),c==null||c()},H=()=>{const je=new Date().getTime()-T.current;C.current=C.current-je,window.clearTimeout(_.current),u==null||u()};return A.addEventListener(oa,H),A.addEventListener(sa,te),()=>{A.removeEventListener(oa,H),A.removeEventListener(sa,te)}}},[y.viewport,E,u,c,F]),d.useEffect(()=>{s&&!y.isClosePausedRef.current&&F(E)},[s,E,y.isClosePausedRef,F]),d.useEffect(()=>(z(),()=>O()),[z,O]);const q=d.useMemo(()=>x?um(x):null,[x]);return y.viewport?i.jsxs(i.Fragment,{children:[q&&i.jsx(k0,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:q}),i.jsx(E0,{scope:n,onClose:Q,children:cl.createPortal(i.jsx(fu.ItemSlot,{scope:n,children:i.jsx(o0,{asChild:!0,onEscapeKeyDown:J(a,()=>{y.isFocusedToastEscapeKeyDownRef.current||Q(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:i.jsx(le.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":y.swipeDirection,...j,ref:f,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:J(e.onKeyDown,A=>{A.key==="Escape"&&(a==null||a(A.nativeEvent),A.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,Q()))}),onPointerDown:J(e.onPointerDown,A=>{A.button===0&&(v.current={x:A.clientX,y:A.clientY})}),onPointerMove:J(e.onPointerMove,A=>{if(!v.current)return;const te=A.clientX-v.current.x,H=A.clientY-v.current.y,je=!!S.current,b=["left","right"].includes(y.swipeDirection),D=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,N=b?D(0,te):0,M=b?0:D(0,H),U=A.pointerType==="touch"?10:2,Ee={x:N,y:M},tt={originalEvent:A,delta:Ee};je?(S.current=Ee,es(w0,p,tt,{discrete:!1})):Xc(Ee,y.swipeDirection,U)?(S.current=Ee,es(x0,m,tt,{discrete:!1}),A.target.setPointerCapture(A.pointerId)):(Math.abs(te)>U||Math.abs(H)>U)&&(v.current=null)}),onPointerUp:J(e.onPointerUp,A=>{const te=S.current,H=A.target;if(H.hasPointerCapture(A.pointerId)&&H.releasePointerCapture(A.pointerId),S.current=null,v.current=null,te){const je=A.currentTarget,b={originalEvent:A,delta:te};Xc(te,y.swipeDirection,y.swipeThreshold)?es(j0,w,b,{discrete:!0}):es(S0,g,b,{discrete:!0}),je.addEventListener("click",D=>D.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),k0=e=>{const{__scopeToast:t,children:n,...r}=e,o=ml(Ro,t),[s,l]=d.useState(!1),[a,u]=d.useState(!1);return _0(()=>l(!0)),d.useEffect(()=>{const c=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(c)},[]),a?null:i.jsx(uu,{asChild:!0,children:i.jsx(cu,{...r,children:s&&i.jsxs(i.Fragment,{children:[o.label," ",n]})})})},T0="ToastTitle",rm=d.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return i.jsx(le.div,{...r,ref:t})});rm.displayName=T0;var P0="ToastDescription",om=d.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return i.jsx(le.div,{...r,ref:t})});om.displayName=P0;var sm="ToastAction",lm=d.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?i.jsx(am,{altText:n,asChild:!0,children:i.jsx(pu,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${sm}\`. Expected non-empty \`string\`.`),null)});lm.displayName=sm;var im="ToastClose",pu=d.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=C0(im,n);return i.jsx(am,{asChild:!0,children:i.jsx(le.button,{type:"button",...r,ref:t,onClick:J(e.onClick,o.onClose)})})});pu.displayName=im;var am=d.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return i.jsx(le.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function um(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),R0(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",s=r.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const l=r.dataset.radixToastAnnounceAlt;l&&t.push(l)}else t.push(...um(r))}}),t}function es(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Kp(o,s):o.dispatchEvent(s)}var Xc=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return t==="left"||t==="right"?s&&r>n:!s&&o>n};function _0(e=()=>{}){const t=Tt(e);wr(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function R0(e){return e.nodeType===e.ELEMENT_NODE}function b0(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ql(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var I0=Zp,cm=em,dm=nm,fm=rm,pm=om,mm=lm,hm=pu;function vm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=vm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function gm(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=vm(e))&&(r&&(r+=" "),r+=t);return r}const Yc=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,qc=gm,bo=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return qc(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,l=Object.keys(o).map(c=>{const m=n==null?void 0:n[c],p=s==null?void 0:s[c];if(m===null)return null;const g=Yc(m)||Yc(p);return o[c][g]}),a=n&&Object.entries(n).reduce((c,m)=>{let[p,g]=m;return g===void 0||(c[p]=g),c},{}),u=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,m)=>{let{class:p,className:g,...w}=m;return Object.entries(w).every(j=>{let[y,x]=j;return Array.isArray(x)?x.includes({...s,...a}[y]):{...s,...a}[y]===x})?[...c,p,g]:c},[]);return qc(e,l,u,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var L0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z0=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=(e,t)=>{const n=d.forwardRef(({color:r="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:a="",children:u,...c},m)=>d.createElement("svg",{ref:m,...L0,width:o,height:o,stroke:r,strokeWidth:l?Number(s)*24/Number(o):s,className:["lucide",`lucide-${z0(e)}`,a].join(" "),...c},[...t.map(([p,g])=>d.createElement(p,g)),...Array.isArray(u)?u:[u]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zc=Ke("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jo=Ke("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hs=Ke("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ia=Ke("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D0=Ke("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jn=Ke("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O0=Ke("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zt=Ke("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ir=Ke("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ym=Ke("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hl=Ke("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const er=Ke("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xm=Ke("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.364.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wm=Ke("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),mu="-",M0=e=>{const t=F0(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:l=>{const a=l.split(mu);return a[0]===""&&a.length!==1&&a.shift(),Sm(a,t)||A0(l)},getConflictingClassGroupIds:(l,a)=>{const u=n[l]||[];return a&&r[l]?[...u,...r[l]]:u}}},Sm=(e,t)=>{var l;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Sm(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(mu);return(l=t.validators.find(({validator:a})=>a(s)))==null?void 0:l.classGroupId},Jc=/^\[(.+)\]$/,A0=e=>{if(Jc.test(e)){const t=Jc.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},F0=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return U0(Object.entries(e.classGroups),n).forEach(([s,l])=>{aa(l,r,s,t)}),r},aa=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:ed(t,o);s.classGroupId=n;return}if(typeof o=="function"){if($0(o)){aa(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,l])=>{aa(l,ed(t,s),n,r)})})},ed=(e,t)=>{let n=e;return t.split(mu).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},$0=e=>e.isThemeGetter,U0=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([l,a])=>[t+l,a])):s);return[n,o]}):e,B0=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,l)=>{n.set(s,l),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let l=n.get(s);if(l!==void 0)return l;if((l=r.get(s))!==void 0)return o(s,l),l},set(s,l){n.has(s)?n.set(s,l):o(s,l)}}},jm="!",V0=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,l=a=>{const u=[];let c=0,m=0,p;for(let x=0;x<a.length;x++){let h=a[x];if(c===0){if(h===o&&(r||a.slice(x,x+s)===t)){u.push(a.slice(m,x)),m=x+s;continue}if(h==="/"){p=x;continue}}h==="["?c++:h==="]"&&c--}const g=u.length===0?a:a.substring(m),w=g.startsWith(jm),j=w?g.substring(1):g,y=p&&p>m?p-m:void 0;return{modifiers:u,hasImportantModifier:w,baseClassName:j,maybePostfixModifierPosition:y}};return n?a=>n({className:a,parseClassName:l}):l},W0=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Q0=e=>({cache:B0(e.cacheSize),parseClassName:V0(e),...M0(e)}),H0=/\s+/,K0=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],l=e.trim().split(H0);let a="";for(let u=l.length-1;u>=0;u-=1){const c=l[u],{modifiers:m,hasImportantModifier:p,baseClassName:g,maybePostfixModifierPosition:w}=n(c);let j=!!w,y=r(j?g.substring(0,w):g);if(!y){if(!j){a=c+(a.length>0?" "+a:a);continue}if(y=r(g),!y){a=c+(a.length>0?" "+a:a);continue}j=!1}const x=W0(m).join(":"),h=p?x+jm:x,f=h+y;if(s.includes(f))continue;s.push(f);const v=o(y,j);for(let S=0;S<v.length;++S){const E=v[S];s.push(h+E)}a=c+(a.length>0?" "+a:a)}return a};function G0(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Em(t))&&(r&&(r+=" "),r+=n);return r}const Em=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Em(e[r]))&&(n&&(n+=" "),n+=t);return n};function X0(e,...t){let n,r,o,s=l;function l(u){const c=t.reduce((m,p)=>p(m),e());return n=Q0(c),r=n.cache.get,o=n.cache.set,s=a,a(u)}function a(u){const c=r(u);if(c)return c;const m=K0(u,n);return o(u,m),m}return function(){return s(G0.apply(null,arguments))}}const ne=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Cm=/^\[(?:([a-z-]+):)?(.+)\]$/i,Y0=/^\d+\/\d+$/,q0=new Set(["px","full","screen"]),Z0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,J0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ew=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,tw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,nw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Pt=e=>ar(e)||q0.has(e)||Y0.test(e),Bt=e=>kr(e,"length",cw),ar=e=>!!e&&!Number.isNaN(Number(e)),Zl=e=>kr(e,"number",ar),Ar=e=>!!e&&Number.isInteger(Number(e)),rw=e=>e.endsWith("%")&&ar(e.slice(0,-1)),$=e=>Cm.test(e),Vt=e=>Z0.test(e),ow=new Set(["length","size","percentage"]),sw=e=>kr(e,ow,Nm),lw=e=>kr(e,"position",Nm),iw=new Set(["image","url"]),aw=e=>kr(e,iw,fw),uw=e=>kr(e,"",dw),Fr=()=>!0,kr=(e,t,n)=>{const r=Cm.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},cw=e=>J0.test(e)&&!ew.test(e),Nm=()=>!1,dw=e=>tw.test(e),fw=e=>nw.test(e),pw=()=>{const e=ne("colors"),t=ne("spacing"),n=ne("blur"),r=ne("brightness"),o=ne("borderColor"),s=ne("borderRadius"),l=ne("borderSpacing"),a=ne("borderWidth"),u=ne("contrast"),c=ne("grayscale"),m=ne("hueRotate"),p=ne("invert"),g=ne("gap"),w=ne("gradientColorStops"),j=ne("gradientColorStopPositions"),y=ne("inset"),x=ne("margin"),h=ne("opacity"),f=ne("padding"),v=ne("saturate"),S=ne("scale"),E=ne("sepia"),T=ne("skew"),C=ne("space"),_=ne("translate"),z=()=>["auto","contain","none"],O=()=>["auto","hidden","clip","visible","scroll"],Q=()=>["auto",$,t],F=()=>[$,t],q=()=>["",Pt,Bt],A=()=>["auto",ar,$],te=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],H=()=>["solid","dashed","dotted","double","none"],je=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],b=()=>["start","end","center","between","around","evenly","stretch"],D=()=>["","0",$],N=()=>["auto","avoid","all","avoid-page","page","left","right","column"],M=()=>[ar,$];return{cacheSize:500,separator:":",theme:{colors:[Fr],spacing:[Pt,Bt],blur:["none","",Vt,$],brightness:M(),borderColor:[e],borderRadius:["none","","full",Vt,$],borderSpacing:F(),borderWidth:q(),contrast:M(),grayscale:D(),hueRotate:M(),invert:D(),gap:F(),gradientColorStops:[e],gradientColorStopPositions:[rw,Bt],inset:Q(),margin:Q(),opacity:M(),padding:F(),saturate:M(),scale:M(),sepia:D(),skew:M(),space:F(),translate:F()},classGroups:{aspect:[{aspect:["auto","square","video",$]}],container:["container"],columns:[{columns:[Vt]}],"break-after":[{"break-after":N()}],"break-before":[{"break-before":N()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...te(),$]}],overflow:[{overflow:O()}],"overflow-x":[{"overflow-x":O()}],"overflow-y":[{"overflow-y":O()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Ar,$]}],basis:[{basis:Q()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",$]}],grow:[{grow:D()}],shrink:[{shrink:D()}],order:[{order:["first","last","none",Ar,$]}],"grid-cols":[{"grid-cols":[Fr]}],"col-start-end":[{col:["auto",{span:["full",Ar,$]},$]}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":[Fr]}],"row-start-end":[{row:["auto",{span:[Ar,$]},$]}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",$]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",$]}],gap:[{gap:[g]}],"gap-x":[{"gap-x":[g]}],"gap-y":[{"gap-y":[g]}],"justify-content":[{justify:["normal",...b()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...b(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...b(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[f]}],px:[{px:[f]}],py:[{py:[f]}],ps:[{ps:[f]}],pe:[{pe:[f]}],pt:[{pt:[f]}],pr:[{pr:[f]}],pb:[{pb:[f]}],pl:[{pl:[f]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[C]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[C]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",$,t]}],"min-w":[{"min-w":[$,t,"min","max","fit"]}],"max-w":[{"max-w":[$,t,"none","full","min","max","fit","prose",{screen:[Vt]},Vt]}],h:[{h:[$,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[$,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[$,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[$,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Vt,Bt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Zl]}],"font-family":[{font:[Fr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",$]}],"line-clamp":[{"line-clamp":["none",ar,Zl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Pt,$]}],"list-image":[{"list-image":["none",$]}],"list-style-type":[{list:["none","disc","decimal",$]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...H(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Pt,Bt]}],"underline-offset":[{"underline-offset":["auto",Pt,$]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:F()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...te(),lw]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",sw]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},aw]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[j]}],"gradient-via-pos":[{via:[j]}],"gradient-to-pos":[{to:[j]}],"gradient-from":[{from:[w]}],"gradient-via":[{via:[w]}],"gradient-to":[{to:[w]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...H(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:H()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...H()]}],"outline-offset":[{"outline-offset":[Pt,$]}],"outline-w":[{outline:[Pt,Bt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[Pt,Bt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Vt,uw]}],"shadow-color":[{shadow:[Fr]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...je(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":je()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",Vt,$]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[m]}],invert:[{invert:[p]}],saturate:[{saturate:[v]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[m]}],"backdrop-invert":[{"backdrop-invert":[p]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",$]}],duration:[{duration:M()}],ease:[{ease:["linear","in","out","in-out",$]}],delay:[{delay:M()}],animate:[{animate:["none","spin","ping","pulse","bounce",$]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[Ar,$]}],"translate-x":[{"translate-x":[_]}],"translate-y":[{"translate-y":[_]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",$]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",$]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":F()}],"scroll-mx":[{"scroll-mx":F()}],"scroll-my":[{"scroll-my":F()}],"scroll-ms":[{"scroll-ms":F()}],"scroll-me":[{"scroll-me":F()}],"scroll-mt":[{"scroll-mt":F()}],"scroll-mr":[{"scroll-mr":F()}],"scroll-mb":[{"scroll-mb":F()}],"scroll-ml":[{"scroll-ml":F()}],"scroll-p":[{"scroll-p":F()}],"scroll-px":[{"scroll-px":F()}],"scroll-py":[{"scroll-py":F()}],"scroll-ps":[{"scroll-ps":F()}],"scroll-pe":[{"scroll-pe":F()}],"scroll-pt":[{"scroll-pt":F()}],"scroll-pr":[{"scroll-pr":F()}],"scroll-pb":[{"scroll-pb":F()}],"scroll-pl":[{"scroll-pl":F()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",$]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Pt,Bt,Zl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},mw=X0(pw);function B(...e){return mw(gm(e))}const hw=I0,km=d.forwardRef(({className:e,...t},n)=>i.jsx(cm,{ref:n,className:B("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));km.displayName=cm.displayName;const vw=bo("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border border-zinc-200 p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full dark:border-zinc-800",{variants:{variant:{default:"border bg-white text-zinc-950 dark:bg-zinc-950 dark:text-zinc-50",destructive:"destructive group border-red-500 bg-red-500 text-zinc-50 dark:border-red-900 dark:bg-red-900 dark:text-zinc-50"}},defaultVariants:{variant:"default"}}),Tm=d.forwardRef(({className:e,variant:t,...n},r)=>i.jsx(dm,{ref:r,className:B(vw({variant:t}),e),...n}));Tm.displayName=dm.displayName;const gw=d.forwardRef(({className:e,...t},n)=>i.jsx(mm,{ref:n,className:B("inline-flex h-8 shrink-0 items-center justify-center rounded-md border border-zinc-200 bg-transparent px-3 text-sm font-medium transition-colors hover:bg-zinc-100 focus:outline-none focus:ring-1 focus:ring-zinc-950 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-zinc-100/40 group-[.destructive]:hover:border-red-500/30 group-[.destructive]:hover:bg-red-500 group-[.destructive]:hover:text-zinc-50 group-[.destructive]:focus:ring-red-500 dark:border-zinc-800 dark:hover:bg-zinc-800 dark:focus:ring-zinc-300 dark:group-[.destructive]:border-zinc-800/40 dark:group-[.destructive]:hover:border-red-900/30 dark:group-[.destructive]:hover:bg-red-900 dark:group-[.destructive]:hover:text-zinc-50 dark:group-[.destructive]:focus:ring-red-900",e),...t}));gw.displayName=mm.displayName;const Pm=d.forwardRef(({className:e,...t},n)=>i.jsx(hm,{ref:n,className:B("absolute right-1 top-1 rounded-md p-1 text-zinc-950/50 opacity-0 transition-opacity hover:text-zinc-950 focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600 dark:text-zinc-50/50 dark:hover:text-zinc-50",e),"toast-close":"",...t,children:i.jsx(wm,{className:"h-4 w-4"})}));Pm.displayName=hm.displayName;const _m=d.forwardRef(({className:e,...t},n)=>i.jsx(fm,{ref:n,className:B("text-sm font-semibold [&+div]:text-xs",e),...t}));_m.displayName=fm.displayName;const Rm=d.forwardRef(({className:e,...t},n)=>i.jsx(pm,{ref:n,className:B("text-sm opacity-90",e),...t}));Rm.displayName=pm.displayName;function yw(){const{toasts:e}=fl();return i.jsxs(hw,{children:[e.map(function({id:t,title:n,description:r,action:o,...s}){return i.jsxs(Tm,{...s,children:[i.jsxs("div",{className:"grid gap-1",children:[n&&i.jsx(_m,{children:n}),r&&i.jsx(Rm,{children:r})]}),o,i.jsx(Pm,{})]},t)}),i.jsx(km,{})]})}const xw=bo("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-zinc-950 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 dark:focus-visible:ring-zinc-300",{variants:{variant:{default:"bg-zinc-900 text-zinc-50 shadow hover:bg-zinc-900/90 dark:bg-zinc-50 dark:text-zinc-900 dark:hover:bg-zinc-50/90",destructive:"bg-red-500 text-zinc-50 shadow-sm hover:bg-red-500/90 dark:bg-red-900 dark:text-zinc-50 dark:hover:bg-red-900/90",outline:"border border-zinc-200 bg-white shadow-sm hover:bg-zinc-100 hover:text-zinc-900 dark:border-zinc-800 dark:bg-zinc-950 dark:hover:bg-zinc-800 dark:hover:text-zinc-50",secondary:"bg-zinc-100 text-zinc-900 shadow-sm hover:bg-zinc-100/80 dark:bg-zinc-800 dark:text-zinc-50 dark:hover:bg-zinc-800/80",ghost:"hover:bg-zinc-100 hover:text-zinc-900 dark:hover:bg-zinc-800 dark:hover:text-zinc-50",link:"text-zinc-900 underline-offset-4 hover:underline dark:text-zinc-50"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),X=d.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>{const l=r?Wx:"button";return i.jsx(l,{className:B(xw({variant:t,size:n,className:e})),ref:s,...o})});X.displayName="Button";const we=d.forwardRef(({className:e,...t},n)=>i.jsx("div",{ref:n,className:B("rounded-xl border border-zinc-200 bg-white text-zinc-950 shadow dark:border-zinc-800 dark:bg-zinc-950 dark:text-zinc-50",e),...t}));we.displayName="Card";const Ie=d.forwardRef(({className:e,...t},n)=>i.jsx("div",{ref:n,className:B("flex flex-col space-y-1.5 p-6",e),...t}));Ie.displayName="CardHeader";const Oe=d.forwardRef(({className:e,...t},n)=>i.jsx("div",{ref:n,className:B("font-semibold leading-none tracking-tight",e),...t}));Oe.displayName="CardTitle";const Be=d.forwardRef(({className:e,...t},n)=>i.jsx("div",{ref:n,className:B("text-sm text-zinc-500 dark:text-zinc-400",e),...t}));Be.displayName="CardDescription";const Me=d.forwardRef(({className:e,...t},n)=>i.jsx("div",{ref:n,className:B("p-6 pt-0",e),...t}));Me.displayName="CardContent";const ww=d.forwardRef(({className:e,...t},n)=>i.jsx("div",{ref:n,className:B("flex items-center p-6 pt-0",e),...t}));ww.displayName="CardFooter";var Sw="Separator",td="horizontal",jw=["horizontal","vertical"],bm=d.forwardRef((e,t)=>{const{decorative:n,orientation:r=td,...o}=e,s=Ew(r)?r:td,a=n?{role:"none"}:{"aria-orientation":s==="vertical"?s:void 0,role:"separator"};return i.jsx(le.div,{"data-orientation":s,...a,...o,ref:t})});bm.displayName=Sw;function Ew(e){return jw.includes(e)}var Im=bm;const Lm=d.forwardRef(({className:e,orientation:t="horizontal",decorative:n=!0,...r},o)=>i.jsx(Im,{ref:o,decorative:n,orientation:t,className:B("shrink-0 bg-zinc-200 dark:bg-zinc-800",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",e),...r}));Lm.displayName=Im.displayName;function Cw(){const e=On(),t=[{name:"知识库选择",href:"/",icon:Jn,current:e.pathname==="/"},{name:"用例经验管理",href:"/usecase",icon:zt,current:e.pathname.startsWith("/usecase")},{name:"SQL经验管理",href:"/sql",icon:hl,current:e.pathname.startsWith("/sql")}];return i.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100",children:[i.jsx("header",{className:"bg-white border-b border-slate-200 shadow-sm",children:i.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:i.jsxs("div",{className:"flex justify-between items-center h-16",children:[i.jsx("div",{className:"flex items-center space-x-4",children:i.jsxs("div",{className:"flex items-center space-x-3",children:[i.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center",children:i.jsx(Jn,{className:"w-5 h-5 text-white"})}),i.jsx("h1",{className:"text-xl font-bold text-slate-900",children:"数据库知识库管理平台"})]})}),i.jsx("div",{className:"flex items-center space-x-4",children:i.jsxs("div",{className:"text-sm text-slate-600",children:["当前时间: ",new Date().toLocaleString("zh-CN")]})})]})})}),i.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:i.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[i.jsx("aside",{className:"lg:col-span-1",children:i.jsxs(we,{className:"p-6",children:[i.jsx("nav",{className:"space-y-2",children:t.map(n=>{const r=n.icon;return i.jsxs(Gl,{to:n.href,className:B("flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",n.current?"bg-blue-50 text-blue-700 border border-blue-200":"text-slate-600 hover:text-slate-900 hover:bg-slate-50"),children:[i.jsx(r,{className:"w-5 h-5"}),i.jsx("span",{children:n.name})]},n.name)})}),i.jsx(Lm,{className:"my-6"}),i.jsxs("div",{className:"space-y-4",children:[i.jsx("h3",{className:"text-sm font-medium text-slate-900",children:"快速操作"}),i.jsxs("div",{className:"space-y-2",children:[i.jsx(X,{variant:"outline",size:"sm",className:"w-full justify-start",asChild:!0,children:i.jsxs(Gl,{to:"/usecase/add",children:[i.jsx(ir,{className:"w-4 h-4 mr-2"}),"添加用例"]})}),i.jsx(X,{variant:"outline",size:"sm",className:"w-full justify-start",asChild:!0,children:i.jsxs(Gl,{to:"/sql/add",children:[i.jsx(ir,{className:"w-4 h-4 mr-2"}),"添加SQL"]})})]})]})]})}),i.jsx("main",{className:"lg:col-span-3",children:i.jsx(Cx,{})})]})}),i.jsx("footer",{className:"bg-white border-t border-slate-200 mt-auto",children:i.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:i.jsx("div",{className:"text-center text-sm text-slate-500",children:"© 2025 数据库知识库管理平台. 版权所有."})})})]})}const nt=d.forwardRef(({className:e,type:t,...n},r)=>i.jsx("input",{type:t,className:B("flex h-9 w-full rounded-md border border-zinc-200 bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-zinc-950 placeholder:text-zinc-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-zinc-950 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:border-zinc-800 dark:file:text-zinc-50 dark:placeholder:text-zinc-400 dark:focus-visible:ring-zinc-300",e),ref:r,...n}));nt.displayName="Input";var Nw="Label",zm=d.forwardRef((e,t)=>i.jsx(le.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));zm.displayName=Nw;var Dm=zm;const kw=bo("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),De=d.forwardRef(({className:e,...t},n)=>i.jsx(Dm,{ref:n,className:B(kw(),e),...t}));De.displayName=Dm.displayName;const Tw=bo("relative w-full rounded-lg border border-zinc-200 px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-zinc-950 [&>svg~*]:pl-7 dark:border-zinc-800 dark:[&>svg]:text-zinc-50",{variants:{variant:{default:"bg-white text-zinc-950 dark:bg-zinc-950 dark:text-zinc-50",destructive:"border-red-500/50 text-red-500 dark:border-red-500 [&>svg]:text-red-500 dark:border-red-900/50 dark:text-red-900 dark:dark:border-red-900 dark:[&>svg]:text-red-900"}},defaultVariants:{variant:"default"}}),ht=d.forwardRef(({className:e,variant:t,...n},r)=>i.jsx("div",{ref:r,role:"alert",className:B(Tw({variant:t}),e),...n}));ht.displayName="Alert";const Pw=d.forwardRef(({className:e,...t},n)=>i.jsx("h5",{ref:n,className:B("mb-1 font-medium leading-none tracking-tight",e),...t}));Pw.displayName="AlertTitle";const vt=d.forwardRef(({className:e,...t},n)=>i.jsx("div",{ref:n,className:B("text-sm [&_p]:leading-relaxed",e),...t}));vt.displayName="AlertDescription";const _w=bo("inline-flex items-center rounded-md border border-zinc-200 px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-zinc-950 focus:ring-offset-2 dark:border-zinc-800 dark:focus:ring-zinc-300",{variants:{variant:{default:"border-transparent bg-zinc-900 text-zinc-50 shadow hover:bg-zinc-900/80 dark:bg-zinc-50 dark:text-zinc-900 dark:hover:bg-zinc-50/80",secondary:"border-transparent bg-zinc-100 text-zinc-900 hover:bg-zinc-100/80 dark:bg-zinc-800 dark:text-zinc-50 dark:hover:bg-zinc-800/80",destructive:"border-transparent bg-red-500 text-zinc-50 shadow hover:bg-red-500/80 dark:bg-red-900 dark:text-zinc-50 dark:hover:bg-red-900/80",outline:"text-zinc-950 dark:text-zinc-50"}},defaultVariants:{variant:"default"}});function ye({className:e,variant:t,...n}){return i.jsx("div",{className:B(_w({variant:t}),e),...n})}function Jl({className:e,...t}){return i.jsx("div",{className:B("animate-pulse rounded-md bg-zinc-900/10 dark:bg-zinc-50/10",e),...t})}const Tr="http://localhost:8000/api",mn={apiKey:"dataset-shmqbk9cnm8xWJ4Aze6Y0Is2",apiIp:"*************"};async function Rw(e=mn){const t={"Content-Type":"application/json"};e.apiKey&&(t["X-API-Key"]=e.apiKey),e.apiIp&&(t["X-API-IP"]=e.apiIp);try{const n=await fetch(`${Tr}/knowledge-base/list`,{method:"GET",headers:t});return n.ok?await n.json():{success:!1,error:(await n.json().catch(()=>({error:"未知错误"}))).error||`请求失败，状态码: ${n.status}`}}catch(n){return{success:!1,error:`网络错误: ${n instanceof Error?n.message:"未知错误"}`}}}async function bw(e,t=mn){try{const n=new URLSearchParams;e.query&&n.append("query",e.query),e.page&&n.append("page",e.page.toString()),e.limit&&n.append("limit",e.limit.toString()),e.knowledge_base_id&&n.append("knowledge_base_id",e.knowledge_base_id);const r=await fetch(`${Tr}/usecase/search?${n}`,{method:"GET",headers:{"Content-Type":"application/json"}});return r.ok?await r.json():{success:!1,error:(await r.json().catch(()=>({error:"搜索失败"}))).error||"搜索失败"}}catch(n){return{success:!1,error:`搜索失败: ${n instanceof Error?n.message:"未知错误"}`}}}async function Iw(e,t=mn){try{const n=new URLSearchParams;e.query&&n.append("query",e.query),e.page&&n.append("page",e.page.toString()),e.limit&&n.append("limit",e.limit.toString()),e.knowledge_base_id&&n.append("knowledge_base_id",e.knowledge_base_id);const r=await fetch(`${Tr}/sql/search?${n}`,{method:"GET",headers:{"Content-Type":"application/json"}});return r.ok?await r.json():{success:!1,error:(await r.json().catch(()=>({error:"搜索失败"}))).error||"搜索失败"}}catch(n){return{success:!1,error:`搜索失败: ${n instanceof Error?n.message:"未知错误"}`}}}async function Lw(e,t,n=mn){try{const r=new FormData;r.append("file",e),r.append("rag_id",t);const o={};n.apiKey&&(o["X-API-Key"]=n.apiKey),n.apiIp&&(o["X-API-IP"]=n.apiIp);const s=await fetch(`${Tr}/usecase/upload`,{method:"POST",headers:o,body:r});return s.ok?await s.json():{success:!1,message:(await s.json().catch(()=>({message:"上传失败"}))).message||"上传失败"}}catch(r){return{success:!1,message:`上传错误: ${r instanceof Error?r.message:"未知错误"}`}}}async function zw(e,t,n=mn){try{const r=new FormData;r.append("file",e),r.append("rag_id",t);const o={};n.apiKey&&(o["X-API-Key"]=n.apiKey),n.apiIp&&(o["X-API-IP"]=n.apiIp);const s=await fetch(`${Tr}/sql/upload`,{method:"POST",headers:o,body:r});return s.ok?await s.json():{success:!1,message:(await s.json().catch(()=>({message:"上传失败"}))).message||"上传失败"}}catch(r){return{success:!1,message:`上传错误: ${r instanceof Error?r.message:"未知错误"}`}}}async function Om(e,t={},n=mn){const r=new AbortController,o=setTimeout(()=>r.abort(),15e3);try{console.log(`[API] 获取文档列表: ${e}`);const s={"Content-Type":"application/json"};n.apiKey&&(s["X-API-Key"]=n.apiKey,console.debug("[API] API密钥:",n.apiKey.substring(0,4)+"****")),n.apiIp&&(s["X-API-IP"]=n.apiIp,console.debug("[API] API IP:",n.apiIp));const l=new URLSearchParams;t.page&&l.set("page",t.page.toString()),t.limit&&l.set("limit",t.limit.toString());const a=new URL(`${Tr}/knowledge-base/${e}/documents`);a.search=l.toString(),console.debug("[API] 请求URL:",a.toString());const u=await fetch(a.toString(),{method:"GET",headers:s,signal:r.signal});if(clearTimeout(o),!u.ok){let c=await u.text();try{c=JSON.parse(c)}catch{}throw new nd(`请求失败: ${u.status} ${u.statusText}`,u.status,c)}try{const c=await u.json();return console.debug("[API] 获取文档成功:",c),{success:!0,data:c}}catch(c){throw new rd("无效的JSON响应",c)}}catch(s){return clearTimeout(o),s instanceof nd||s instanceof rd?{success:!1,error:s.message}:s.name==="AbortError"?{success:!1,error:"请求超时 (15秒)"}:s instanceof TypeError&&s.message.includes("fetch")?{success:!1,error:"网络连接失败，请检查API服务器是否可用"}:{success:!1,error:`未知错误: ${s instanceof Error?s.message:JSON.stringify(s)}`}}}async function Mm(e,t,n={},r=mn){var l;const o=new AbortController,s=setTimeout(()=>o.abort(),3e4);try{console.log(`[API] 获取文档分段: 知识库ID=${e}, 文档ID=${t}`);const a={Authorization:`Bearer ${r.apiKey}`,"Content-Type":"application/json"},u=new URLSearchParams;n.query&&u.set("query",n.query),n.page&&u.set("page",n.page.toString()),n.limit&&u.set("limit",n.limit.toString());const c=`http://${r.apiIp}/v1/datasets/${e}/documents/${t}/segments`,m=u.toString()?`${c}?${u.toString()}`:c;console.debug("[API] 请求URL:",m);const p=await fetch(m,{method:"GET",headers:a,signal:o.signal});if(clearTimeout(s),!p.ok){let w=await p.text();try{w=JSON.parse(w)}catch{}return console.error(`[API] 获取文档分段失败: ${p.status}`,w),{success:!1,error:`HTTP ${p.status}: ${typeof w=="object"&&w&&"message"in w?w.message:w||p.statusText}`}}const g=await p.json();return console.log("[API] 获取文档分段成功:",{documentId:t,query:n.query,totalReturned:((l=g.data)==null?void 0:l.length)||0,hasMore:g.has_more,total:g.total}),{success:!0,data:g}}catch(a){return clearTimeout(s),console.error("[API] 获取文档分段异常:",a),a.name==="AbortError"?{success:!1,error:"请求超时，请检查网络连接"}:{success:!1,error:a.message||"获取文档分段时发生未知错误"}}}async function Dw(e,t,n,r=mn){try{console.log(`[API] 搜索所有文档分段: 知识库ID=${e}, 查询词="${t}", 文档数=${n.length}`);const o=n.map(u=>Mm(e,u,{page:1,limit:100},r)),s=await Promise.allSettled(o),l=[];s.forEach((u,c)=>{if(u.status==="fulfilled"&&u.value.success&&u.value.data){const m=u.value.data.data;console.log(`[API] 文档 ${n[c]} 返回 ${m.length} 个分段`);const p=m.filter(g=>{const w=t.toLowerCase(),j=g.content.toLowerCase().includes(w),y=g.answer&&g.answer.toLowerCase().includes(w),x=g.keywords.some(f=>f.toLowerCase().includes(w)),h=j||y||x;return h&&console.log(`[API] 匹配分段在文档 ${n[c]}:`,{position:g.position,contentPreview:g.content.substring(0,100)+"...",matchedIn:{content:j,answer:y,keywords:x}}),h});if(p.length>0){const g={...u.value.data,data:p,total:p.length};l.push(g)}}else u.status==="rejected"?console.warn(`[API] 文档 ${n[c]} 查询失败:`,u.reason):u.status==="fulfilled"&&!u.value.success&&console.warn(`[API] 文档 ${n[c]} API调用失败:`,u.value.error)});const a=l.reduce((u,c)=>u+c.data.length,0);return console.log(`[API] 搜索完成，找到 ${l.length} 个文档包含 ${a} 个匹配分段`),{success:!0,data:l}}catch(o){return console.error("[API] 搜索所有文档分段异常:",o),{success:!1,error:o.message||"搜索文档分段时发生未知错误"}}}class nd extends Error{constructor(t,n,r){super(t),this.statusCode=n,this.details=r,this.name="RequestError"}}class rd extends Error{constructor(t,n){super(t),this.originalError=n,this.name="ResponseError"}}function Ow(e){if(e.length===0)return"";const t=["id","name","description","type","db_type","content"],n=e.map(o=>[o.id,o.name,o.description,o.type,o.db_type,Array.isArray(o.content)?`[${o.content.map(s=>`'${s.replace(/'/g,"''")}'`).join(",")}]`:`'${String(o.content).replace(/'/g,"''")}'`]);return[t,...n].map(o=>o.map(s=>`"${s}"`).join(",")).join(`
`)}function Am(e,t,n){const r=new Blob([e],{type:n});return new File([r],t,{type:n})}function Mw(){const[e,t]=d.useState([]),[n,r]=d.useState([]),[o,s]=d.useState(!0),[l,a]=d.useState(null),[u,c]=d.useState(""),[m,p]=d.useState(null),{toast:g}=fl(),w=$p();d.useEffect(()=>{j()},[]),d.useEffect(()=>{const f=e.filter(v=>v.name.toLowerCase().includes(u.toLowerCase())||v.description&&v.description.toLowerCase().includes(u.toLowerCase()));r(f)},[u,e]);const j=async()=>{s(!0),a(null);try{const f=await Rw();f.success&&f.data?(t(f.data),r(f.data)):a(f.error||"获取知识库列表失败")}catch{a("网络错误，请检查连接后重试")}finally{s(!1)}},y=f=>{p(f),g({title:"已选择知识库",description:`${f.name} - ${f.type==="usecase"?"用例经验知识库":"SQL经验知识库"}`}),setTimeout(()=>{f.type==="usecase"?w("/usecase",{state:{knowledgeBase:f}}):f.type==="sql"&&w("/sql",{state:{knowledgeBase:f}})},1e3)},x=f=>{switch(f){case"usecase":return i.jsx(zt,{className:"w-5 h-5"});case"sql":return i.jsx(ia,{className:"w-5 h-5"});default:return i.jsx(Jn,{className:"w-5 h-5"})}},h=f=>{switch(f){case"usecase":return"default";case"sql":return"secondary";default:return"outline"}};return o?i.jsxs("div",{className:"space-y-6",children:[i.jsx(we,{children:i.jsxs(Ie,{children:[i.jsxs(Oe,{className:"flex items-center space-x-2",children:[i.jsx(Jn,{className:"w-6 h-6"}),i.jsx("span",{children:"知识库选择"})]}),i.jsx(Be,{children:"选择要管理的知识库类型"})]})}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((f,v)=>i.jsxs(we,{children:[i.jsxs(Ie,{children:[i.jsx(Jl,{className:"h-6 w-3/4"}),i.jsx(Jl,{className:"h-4 w-full"})]}),i.jsx(Me,{children:i.jsx(Jl,{className:"h-10 w-full"})})]},v))})]}):i.jsxs("div",{className:"space-y-6",children:[i.jsxs(we,{children:[i.jsxs(Ie,{children:[i.jsxs(Oe,{className:"flex items-center space-x-2",children:[i.jsx(Jn,{className:"w-6 h-6"}),i.jsx("span",{children:"知识库选择"})]}),i.jsx(Be,{children:"选择要管理的知识库类型：用例经验知识库或SQL经验知识库"})]}),i.jsx(Me,{children:i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsxs("div",{className:"flex-1",children:[i.jsx(De,{htmlFor:"search",children:"搜索知识库"}),i.jsxs("div",{className:"relative mt-1",children:[i.jsx(hl,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4"}),i.jsx(nt,{id:"search",placeholder:"输入知识库名称或描述...",value:u,onChange:f=>c(f.target.value),className:"pl-10"})]})]}),i.jsx(X,{onClick:j,variant:"outline",children:"刷新列表"})]})})]}),l&&i.jsxs(ht,{variant:"destructive",children:[i.jsx(jo,{className:"h-4 w-4"}),i.jsx(vt,{children:l})]}),m&&i.jsxs(ht,{children:[i.jsx(Hs,{className:"h-4 w-4"}),i.jsxs(vt,{children:["已选择知识库: ",m.name,"，正在跳转到管理页面..."]})]}),n.length===0&&!o?i.jsx(we,{children:i.jsxs(Me,{className:"flex flex-col items-center justify-center py-12",children:[i.jsx(Jn,{className:"w-12 h-12 text-slate-400 mb-4"}),i.jsx("h3",{className:"text-lg font-medium text-slate-900 mb-2",children:"没有找到知识库"}),i.jsx("p",{className:"text-slate-500 text-center mb-4",children:u?"请尝试其他搜索关键词":"暂无可用的知识库"}),u&&i.jsx(X,{variant:"outline",onClick:()=>c(""),children:"清除搜索"})]})}):i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map(f=>i.jsxs(we,{className:`cursor-pointer transition-all hover:shadow-lg ${(m==null?void 0:m.id)===f.id?"ring-2 ring-blue-500 bg-blue-50":""}`,onClick:()=>y(f),children:[i.jsxs(Ie,{children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[x(f.type),i.jsx(Oe,{className:"text-lg",children:f.name})]}),i.jsx(ye,{variant:h(f.type),children:f.type==="usecase"?"用例经验":"SQL经验"})]}),f.description&&i.jsx(Be,{className:"line-clamp-2",children:f.description})]}),i.jsx(Me,{children:i.jsxs("div",{className:"space-y-3",children:[i.jsxs("div",{className:"flex items-center justify-between text-sm text-slate-500",children:[i.jsxs("span",{children:["ID: ",f.id]}),i.jsxs("span",{children:["更新: ",new Date().toLocaleDateString("zh-CN")]})]}),i.jsx(X,{className:"w-full",size:"sm",onClick:v=>{v.stopPropagation(),y(f)},children:"选择此知识库"})]})})]},f.id))}),i.jsx(we,{children:i.jsx(Me,{className:"py-4",children:i.jsxs("div",{className:"flex items-center justify-between text-sm text-slate-600",children:[i.jsxs("span",{children:["共找到 ",n.length," 个知识库",u&&` (搜索: "${u}")`]}),i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsxs("span",{className:"flex items-center space-x-1",children:[i.jsx(zt,{className:"w-4 h-4"}),i.jsxs("span",{children:[n.filter(f=>f.type==="usecase").length," 个用例经验"]})]}),i.jsxs("span",{className:"flex items-center space-x-1",children:[i.jsx(ia,{className:"w-4 h-4"}),i.jsxs("span",{children:[n.filter(f=>f.type==="sql").length," 个SQL经验"]})]})]})]})})})]})}const hu=d.forwardRef(({className:e,...t},n)=>i.jsx("textarea",{className:B("flex min-h-[60px] w-full rounded-md border border-zinc-200 bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-zinc-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-zinc-950 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:border-zinc-800 dark:placeholder:text-zinc-400 dark:focus-visible:ring-zinc-300",e),ref:n,...t}));hu.displayName="Textarea";var Aw=ha[" useId ".trim().toString()]||(()=>{}),Fw=0;function eo(e){const[t,n]=d.useState(Aw());return wr(()=>{n(r=>r??String(Fw++))},[e]),t?`radix-${t}`:""}var $w=d.createContext(void 0);function Fm(e){const t=d.useContext($w);return e||t||"ltr"}var ei="rovingFocusGroup.onEntryFocus",Uw={bubbles:!1,cancelable:!0},Io="RovingFocusGroup",[ua,$m,Bw]=Hp(Io),[Vw,Um]=_o(Io,[Bw]),[Ww,Qw]=Vw(Io),Bm=d.forwardRef((e,t)=>i.jsx(ua.Provider,{scope:e.__scopeRovingFocusGroup,children:i.jsx(ua.Slot,{scope:e.__scopeRovingFocusGroup,children:i.jsx(Hw,{...e,ref:t})})}));Bm.displayName=Io;var Hw=d.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:l,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:u,onEntryFocus:c,preventScrollOnEntryFocus:m=!1,...p}=e,g=d.useRef(null),w=ut(t,g),j=Fm(s),[y,x]=pl({prop:l,defaultProp:a??null,onChange:u,caller:Io}),[h,f]=d.useState(!1),v=Tt(c),S=$m(n),E=d.useRef(!1),[T,C]=d.useState(0);return d.useEffect(()=>{const _=g.current;if(_)return _.addEventListener(ei,v),()=>_.removeEventListener(ei,v)},[v]),i.jsx(Ww,{scope:n,orientation:r,dir:j,loop:o,currentTabStopId:y,onItemFocus:d.useCallback(_=>x(_),[x]),onItemShiftTab:d.useCallback(()=>f(!0),[]),onFocusableItemAdd:d.useCallback(()=>C(_=>_+1),[]),onFocusableItemRemove:d.useCallback(()=>C(_=>_-1),[]),children:i.jsx(le.div,{tabIndex:h||T===0?-1:0,"data-orientation":r,...p,ref:w,style:{outline:"none",...e.style},onMouseDown:J(e.onMouseDown,()=>{E.current=!0}),onFocus:J(e.onFocus,_=>{const z=!E.current;if(_.target===_.currentTarget&&z&&!h){const O=new CustomEvent(ei,Uw);if(_.currentTarget.dispatchEvent(O),!O.defaultPrevented){const Q=S().filter(H=>H.focusable),F=Q.find(H=>H.active),q=Q.find(H=>H.id===y),te=[F,q,...Q].filter(Boolean).map(H=>H.ref.current);Qm(te,m)}}E.current=!1}),onBlur:J(e.onBlur,()=>f(!1))})})}),Vm="RovingFocusGroupItem",Wm=d.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,children:l,...a}=e,u=eo(),c=s||u,m=Qw(Vm,n),p=m.currentTabStopId===c,g=$m(n),{onFocusableItemAdd:w,onFocusableItemRemove:j,currentTabStopId:y}=m;return d.useEffect(()=>{if(r)return w(),()=>j()},[r,w,j]),i.jsx(ua.ItemSlot,{scope:n,id:c,focusable:r,active:o,children:i.jsx(le.span,{tabIndex:p?0:-1,"data-orientation":m.orientation,...a,ref:t,onMouseDown:J(e.onMouseDown,x=>{r?m.onItemFocus(c):x.preventDefault()}),onFocus:J(e.onFocus,()=>m.onItemFocus(c)),onKeyDown:J(e.onKeyDown,x=>{if(x.key==="Tab"&&x.shiftKey){m.onItemShiftTab();return}if(x.target!==x.currentTarget)return;const h=Xw(x,m.orientation,m.dir);if(h!==void 0){if(x.metaKey||x.ctrlKey||x.altKey||x.shiftKey)return;x.preventDefault();let v=g().filter(S=>S.focusable).map(S=>S.ref.current);if(h==="last")v.reverse();else if(h==="prev"||h==="next"){h==="prev"&&v.reverse();const S=v.indexOf(x.currentTarget);v=m.loop?Yw(v,S+1):v.slice(S+1)}setTimeout(()=>Qm(v))}}),children:typeof l=="function"?l({isCurrentTabStop:p,hasTabStop:y!=null}):l})})});Wm.displayName=Vm;var Kw={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Gw(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Xw(e,t,n){const r=Gw(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return Kw[r]}function Qm(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function Yw(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var qw=Bm,Zw=Wm,vl="Tabs",[Jw,hS]=_o(vl,[Um]),Hm=Um(),[e1,vu]=Jw(vl),Km=d.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:s,orientation:l="horizontal",dir:a,activationMode:u="automatic",...c}=e,m=Fm(a),[p,g]=pl({prop:r,onChange:o,defaultProp:s??"",caller:vl});return i.jsx(e1,{scope:n,baseId:eo(),value:p,onValueChange:g,orientation:l,dir:m,activationMode:u,children:i.jsx(le.div,{dir:m,"data-orientation":l,...c,ref:t})})});Km.displayName=vl;var Gm="TabsList",Xm=d.forwardRef((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,s=vu(Gm,n),l=Hm(n);return i.jsx(qw,{asChild:!0,...l,orientation:s.orientation,dir:s.dir,loop:r,children:i.jsx(le.div,{role:"tablist","aria-orientation":s.orientation,...o,ref:t})})});Xm.displayName=Gm;var Ym="TabsTrigger",qm=d.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...s}=e,l=vu(Ym,n),a=Hm(n),u=eh(l.baseId,r),c=th(l.baseId,r),m=r===l.value;return i.jsx(Zw,{asChild:!0,...a,focusable:!o,active:m,children:i.jsx(le.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":c,"data-state":m?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:u,...s,ref:t,onMouseDown:J(e.onMouseDown,p=>{!o&&p.button===0&&p.ctrlKey===!1?l.onValueChange(r):p.preventDefault()}),onKeyDown:J(e.onKeyDown,p=>{[" ","Enter"].includes(p.key)&&l.onValueChange(r)}),onFocus:J(e.onFocus,()=>{const p=l.activationMode!=="manual";!m&&!o&&p&&l.onValueChange(r)})})})});qm.displayName=Ym;var Zm="TabsContent",Jm=d.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,forceMount:o,children:s,...l}=e,a=vu(Zm,n),u=eh(a.baseId,r),c=th(a.baseId,r),m=r===a.value,p=d.useRef(m);return d.useEffect(()=>{const g=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(g)},[]),i.jsx(Nr,{present:o||m,children:({present:g})=>i.jsx(le.div,{"data-state":m?"active":"inactive","data-orientation":a.orientation,role:"tabpanel","aria-labelledby":u,hidden:!g,id:c,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:g&&s})})});Jm.displayName=Zm;function eh(e,t){return`${e}-trigger-${t}`}function th(e,t){return`${e}-content-${t}`}var t1=Km,nh=Xm,rh=qm,oh=Jm;const sh=t1,gu=d.forwardRef(({className:e,...t},n)=>i.jsx(nh,{ref:n,className:B("inline-flex h-9 items-center justify-center rounded-lg bg-zinc-100 p-1 text-zinc-500 dark:bg-zinc-800 dark:text-zinc-400",e),...t}));gu.displayName=nh.displayName;const Nn=d.forwardRef(({className:e,...t},n)=>i.jsx(rh,{ref:n,className:B("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-zinc-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-white data-[state=active]:text-zinc-950 data-[state=active]:shadow dark:ring-offset-zinc-950 dark:focus-visible:ring-zinc-300 dark:data-[state=active]:bg-zinc-950 dark:data-[state=active]:text-zinc-50",e),...t}));Nn.displayName=rh.displayName;const kn=d.forwardRef(({className:e,...t},n)=>i.jsx(oh,{ref:n,className:B("mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-zinc-950 focus-visible:ring-offset-2 dark:ring-offset-zinc-950 dark:focus-visible:ring-zinc-300",e),...t}));kn.displayName=oh.displayName;const ur=d.forwardRef(({className:e,...t},n)=>i.jsx("div",{className:"relative w-full overflow-auto",children:i.jsx("table",{ref:n,className:B("w-full caption-bottom text-sm",e),...t})}));ur.displayName="Table";const cr=d.forwardRef(({className:e,...t},n)=>i.jsx("thead",{ref:n,className:B("[&_tr]:border-b",e),...t}));cr.displayName="TableHeader";const dr=d.forwardRef(({className:e,...t},n)=>i.jsx("tbody",{ref:n,className:B("[&_tr:last-child]:border-0",e),...t}));dr.displayName="TableBody";const n1=d.forwardRef(({className:e,...t},n)=>i.jsx("tfoot",{ref:n,className:B("border-t bg-zinc-100/50 font-medium [&>tr]:last:border-b-0 dark:bg-zinc-800/50",e),...t}));n1.displayName="TableFooter";const mt=d.forwardRef(({className:e,...t},n)=>i.jsx("tr",{ref:n,className:B("border-b transition-colors hover:bg-zinc-100/50 data-[state=selected]:bg-zinc-100 dark:hover:bg-zinc-800/50 dark:data-[state=selected]:bg-zinc-800",e),...t}));mt.displayName="TableRow";const K=d.forwardRef(({className:e,...t},n)=>i.jsx("th",{ref:n,className:B("h-10 px-2 text-left align-middle font-medium text-zinc-500 [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] dark:text-zinc-400",e),...t}));K.displayName="TableHead";const G=d.forwardRef(({className:e,...t},n)=>i.jsx("td",{ref:n,className:B("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));G.displayName="TableCell";const r1=d.forwardRef(({className:e,...t},n)=>i.jsx("caption",{ref:n,className:B("mt-4 text-sm text-zinc-500 dark:text-zinc-400",e),...t}));r1.displayName="TableCaption";var ti="focusScope.autoFocusOnMount",ni="focusScope.autoFocusOnUnmount",od={bubbles:!1,cancelable:!0},o1="FocusScope",lh=d.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...l}=e,[a,u]=d.useState(null),c=Tt(o),m=Tt(s),p=d.useRef(null),g=ut(t,y=>u(y)),w=d.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;d.useEffect(()=>{if(r){let y=function(v){if(w.paused||!a)return;const S=v.target;a.contains(S)?p.current=S:Wt(p.current,{select:!0})},x=function(v){if(w.paused||!a)return;const S=v.relatedTarget;S!==null&&(a.contains(S)||Wt(p.current,{select:!0}))},h=function(v){if(document.activeElement===document.body)for(const E of v)E.removedNodes.length>0&&Wt(a)};document.addEventListener("focusin",y),document.addEventListener("focusout",x);const f=new MutationObserver(h);return a&&f.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",y),document.removeEventListener("focusout",x),f.disconnect()}}},[r,a,w.paused]),d.useEffect(()=>{if(a){ld.add(w);const y=document.activeElement;if(!a.contains(y)){const h=new CustomEvent(ti,od);a.addEventListener(ti,c),a.dispatchEvent(h),h.defaultPrevented||(s1(c1(ih(a)),{select:!0}),document.activeElement===y&&Wt(a))}return()=>{a.removeEventListener(ti,c),setTimeout(()=>{const h=new CustomEvent(ni,od);a.addEventListener(ni,m),a.dispatchEvent(h),h.defaultPrevented||Wt(y??document.body,{select:!0}),a.removeEventListener(ni,m),ld.remove(w)},0)}}},[a,c,m,w]);const j=d.useCallback(y=>{if(!n&&!r||w.paused)return;const x=y.key==="Tab"&&!y.altKey&&!y.ctrlKey&&!y.metaKey,h=document.activeElement;if(x&&h){const f=y.currentTarget,[v,S]=l1(f);v&&S?!y.shiftKey&&h===S?(y.preventDefault(),n&&Wt(v,{select:!0})):y.shiftKey&&h===v&&(y.preventDefault(),n&&Wt(S,{select:!0})):h===f&&y.preventDefault()}},[n,r,w.paused]);return i.jsx(le.div,{tabIndex:-1,...l,ref:g,onKeyDown:j})});lh.displayName=o1;function s1(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Wt(r,{select:t}),document.activeElement!==n)return}function l1(e){const t=ih(e),n=sd(t,e),r=sd(t.reverse(),e);return[n,r]}function ih(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function sd(e,t){for(const n of e)if(!i1(n,{upTo:t}))return n}function i1(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function a1(e){return e instanceof HTMLInputElement&&"select"in e}function Wt(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&a1(e)&&t&&e.select()}}var ld=u1();function u1(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=id(e,t),e.unshift(t)},remove(t){var n;e=id(e,t),(n=e[0])==null||n.resume()}}}function id(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function c1(e){return e.filter(t=>t.tagName!=="A")}var ri=0;function d1(){d.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ad()),document.body.insertAdjacentElement("beforeend",e[1]??ad()),ri++,()=>{ri===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),ri--}},[])}function ad(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Ct=function(){return Ct=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Ct.apply(this,arguments)};function ah(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function f1(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var xs="right-scroll-bar-position",ws="width-before-scroll-bar",p1="with-scroll-bars-hidden",m1="--removed-body-scroll-bar-size";function oi(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function h1(e,t){var n=d.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var v1=typeof window<"u"?d.useLayoutEffect:d.useEffect,ud=new WeakMap;function g1(e,t){var n=h1(null,function(r){return e.forEach(function(o){return oi(o,r)})});return v1(function(){var r=ud.get(n);if(r){var o=new Set(r),s=new Set(e),l=n.current;o.forEach(function(a){s.has(a)||oi(a,null)}),s.forEach(function(a){o.has(a)||oi(a,l)})}ud.set(n,e)},[e]),n}function y1(e){return e}function x1(e,t){t===void 0&&(t=y1);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var l=t(s,r);return n.push(l),function(){n=n.filter(function(a){return a!==l})}},assignSyncMedium:function(s){for(r=!0;n.length;){var l=n;n=[],l.forEach(s)}n={push:function(a){return s(a)},filter:function(){return n}}},assignMedium:function(s){r=!0;var l=[];if(n.length){var a=n;n=[],a.forEach(s),l=n}var u=function(){var m=l;l=[],m.forEach(s)},c=function(){return Promise.resolve().then(u)};c(),n={push:function(m){l.push(m),c()},filter:function(m){return l=l.filter(m),n}}}};return o}function w1(e){e===void 0&&(e={});var t=x1(null);return t.options=Ct({async:!0,ssr:!1},e),t}var uh=function(e){var t=e.sideCar,n=ah(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return d.createElement(r,Ct({},n))};uh.isSideCarExport=!0;function S1(e,t){return e.useMedium(t),uh}var ch=w1(),si=function(){},gl=d.forwardRef(function(e,t){var n=d.useRef(null),r=d.useState({onScrollCapture:si,onWheelCapture:si,onTouchMoveCapture:si}),o=r[0],s=r[1],l=e.forwardProps,a=e.children,u=e.className,c=e.removeScrollBar,m=e.enabled,p=e.shards,g=e.sideCar,w=e.noRelative,j=e.noIsolation,y=e.inert,x=e.allowPinchZoom,h=e.as,f=h===void 0?"div":h,v=e.gapMode,S=ah(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=g,T=g1([n,t]),C=Ct(Ct({},S),o);return d.createElement(d.Fragment,null,m&&d.createElement(E,{sideCar:ch,removeScrollBar:c,shards:p,noRelative:w,noIsolation:j,inert:y,setCallbacks:s,allowPinchZoom:!!x,lockRef:n,gapMode:v}),l?d.cloneElement(d.Children.only(a),Ct(Ct({},C),{ref:T})):d.createElement(f,Ct({},C,{className:u,ref:T}),a))});gl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};gl.classNames={fullWidth:ws,zeroRight:xs};var j1=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function E1(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=j1();return t&&e.setAttribute("nonce",t),e}function C1(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function N1(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var k1=function(){var e=0,t=null;return{add:function(n){e==0&&(t=E1())&&(C1(t,n),N1(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},T1=function(){var e=k1();return function(t,n){d.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},dh=function(){var e=T1(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},P1={left:0,top:0,right:0,gap:0},li=function(e){return parseInt(e||"",10)||0},_1=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[li(n),li(r),li(o)]},R1=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return P1;var t=_1(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},b1=dh(),fr="data-scroll-locked",I1=function(e,t,n,r){var o=e.left,s=e.top,l=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(p1,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(fr,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(l,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(xs,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(ws,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(xs," .").concat(xs,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(ws," .").concat(ws,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(fr,`] {
    `).concat(m1,": ").concat(a,`px;
  }
`)},cd=function(){var e=parseInt(document.body.getAttribute(fr)||"0",10);return isFinite(e)?e:0},L1=function(){d.useEffect(function(){return document.body.setAttribute(fr,(cd()+1).toString()),function(){var e=cd()-1;e<=0?document.body.removeAttribute(fr):document.body.setAttribute(fr,e.toString())}},[])},z1=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;L1();var s=d.useMemo(function(){return R1(o)},[o]);return d.createElement(b1,{styles:I1(s,!t,o,n?"":"!important")})},ca=!1;if(typeof window<"u")try{var ts=Object.defineProperty({},"passive",{get:function(){return ca=!0,!0}});window.addEventListener("test",ts,ts),window.removeEventListener("test",ts,ts)}catch{ca=!1}var An=ca?{passive:!1}:!1,D1=function(e){return e.tagName==="TEXTAREA"},fh=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!D1(e)&&n[t]==="visible")},O1=function(e){return fh(e,"overflowY")},M1=function(e){return fh(e,"overflowX")},dd=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=ph(e,r);if(o){var s=mh(e,r),l=s[1],a=s[2];if(l>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},A1=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},F1=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},ph=function(e,t){return e==="v"?O1(t):M1(t)},mh=function(e,t){return e==="v"?A1(t):F1(t)},$1=function(e,t){return e==="h"&&t==="rtl"?-1:1},U1=function(e,t,n,r,o){var s=$1(e,window.getComputedStyle(t).direction),l=s*r,a=n.target,u=t.contains(a),c=!1,m=l>0,p=0,g=0;do{if(!a)break;var w=mh(e,a),j=w[0],y=w[1],x=w[2],h=y-x-s*j;(j||h)&&ph(e,a)&&(p+=h,g+=j);var f=a.parentNode;a=f&&f.nodeType===Node.DOCUMENT_FRAGMENT_NODE?f.host:f}while(!u&&a!==document.body||u&&(t.contains(a)||t===a));return(m&&Math.abs(p)<1||!m&&Math.abs(g)<1)&&(c=!0),c},ns=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},fd=function(e){return[e.deltaX,e.deltaY]},pd=function(e){return e&&"current"in e?e.current:e},B1=function(e,t){return e[0]===t[0]&&e[1]===t[1]},V1=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},W1=0,Fn=[];function Q1(e){var t=d.useRef([]),n=d.useRef([0,0]),r=d.useRef(),o=d.useState(W1++)[0],s=d.useState(dh)[0],l=d.useRef(e);d.useEffect(function(){l.current=e},[e]),d.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var y=f1([e.lockRef.current],(e.shards||[]).map(pd),!0).filter(Boolean);return y.forEach(function(x){return x.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),y.forEach(function(x){return x.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=d.useCallback(function(y,x){if("touches"in y&&y.touches.length===2||y.type==="wheel"&&y.ctrlKey)return!l.current.allowPinchZoom;var h=ns(y),f=n.current,v="deltaX"in y?y.deltaX:f[0]-h[0],S="deltaY"in y?y.deltaY:f[1]-h[1],E,T=y.target,C=Math.abs(v)>Math.abs(S)?"h":"v";if("touches"in y&&C==="h"&&T.type==="range")return!1;var _=dd(C,T);if(!_)return!0;if(_?E=C:(E=C==="v"?"h":"v",_=dd(C,T)),!_)return!1;if(!r.current&&"changedTouches"in y&&(v||S)&&(r.current=E),!E)return!0;var z=r.current||E;return U1(z,x,y,z==="h"?v:S)},[]),u=d.useCallback(function(y){var x=y;if(!(!Fn.length||Fn[Fn.length-1]!==s)){var h="deltaY"in x?fd(x):ns(x),f=t.current.filter(function(E){return E.name===x.type&&(E.target===x.target||x.target===E.shadowParent)&&B1(E.delta,h)})[0];if(f&&f.should){x.cancelable&&x.preventDefault();return}if(!f){var v=(l.current.shards||[]).map(pd).filter(Boolean).filter(function(E){return E.contains(x.target)}),S=v.length>0?a(x,v[0]):!l.current.noIsolation;S&&x.cancelable&&x.preventDefault()}}},[]),c=d.useCallback(function(y,x,h,f){var v={name:y,delta:x,target:h,should:f,shadowParent:H1(h)};t.current.push(v),setTimeout(function(){t.current=t.current.filter(function(S){return S!==v})},1)},[]),m=d.useCallback(function(y){n.current=ns(y),r.current=void 0},[]),p=d.useCallback(function(y){c(y.type,fd(y),y.target,a(y,e.lockRef.current))},[]),g=d.useCallback(function(y){c(y.type,ns(y),y.target,a(y,e.lockRef.current))},[]);d.useEffect(function(){return Fn.push(s),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:g}),document.addEventListener("wheel",u,An),document.addEventListener("touchmove",u,An),document.addEventListener("touchstart",m,An),function(){Fn=Fn.filter(function(y){return y!==s}),document.removeEventListener("wheel",u,An),document.removeEventListener("touchmove",u,An),document.removeEventListener("touchstart",m,An)}},[]);var w=e.removeScrollBar,j=e.inert;return d.createElement(d.Fragment,null,j?d.createElement(s,{styles:V1(o)}):null,w?d.createElement(z1,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function H1(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const K1=S1(ch,Q1);var hh=d.forwardRef(function(e,t){return d.createElement(gl,Ct({},e,{ref:t,sideCar:K1}))});hh.classNames=gl.classNames;var G1=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},$n=new WeakMap,rs=new WeakMap,os={},ii=0,vh=function(e){return e&&(e.host||vh(e.parentNode))},X1=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=vh(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Y1=function(e,t,n,r){var o=X1(t,Array.isArray(e)?e:[e]);os[n]||(os[n]=new WeakMap);var s=os[n],l=[],a=new Set,u=new Set(o),c=function(p){!p||a.has(p)||(a.add(p),c(p.parentNode))};o.forEach(c);var m=function(p){!p||u.has(p)||Array.prototype.forEach.call(p.children,function(g){if(a.has(g))m(g);else try{var w=g.getAttribute(r),j=w!==null&&w!=="false",y=($n.get(g)||0)+1,x=(s.get(g)||0)+1;$n.set(g,y),s.set(g,x),l.push(g),y===1&&j&&rs.set(g,!0),x===1&&g.setAttribute(n,"true"),j||g.setAttribute(r,"true")}catch(h){console.error("aria-hidden: cannot operate on ",g,h)}})};return m(t),a.clear(),ii++,function(){l.forEach(function(p){var g=$n.get(p)-1,w=s.get(p)-1;$n.set(p,g),s.set(p,w),g||(rs.has(p)||p.removeAttribute(r),rs.delete(p)),w||p.removeAttribute(n)}),ii--,ii||($n=new WeakMap,$n=new WeakMap,rs=new WeakMap,os={})}},q1=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=G1(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Y1(r,o,n,"aria-hidden")):function(){return null}},yl="Dialog",[gh,vS]=_o(yl),[Z1,wt]=gh(yl),yh=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:l=!0}=e,a=d.useRef(null),u=d.useRef(null),[c,m]=pl({prop:r,defaultProp:o??!1,onChange:s,caller:yl});return i.jsx(Z1,{scope:t,triggerRef:a,contentRef:u,contentId:eo(),titleId:eo(),descriptionId:eo(),open:c,onOpenChange:m,onOpenToggle:d.useCallback(()=>m(p=>!p),[m]),modal:l,children:n})};yh.displayName=yl;var xh="DialogTrigger",wh=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=wt(xh,n),s=ut(t,o.triggerRef);return i.jsx(le.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":wu(o.open),...r,ref:s,onClick:J(e.onClick,o.onOpenToggle)})});wh.displayName=xh;var yu="DialogPortal",[J1,Sh]=gh(yu,{forceMount:void 0}),jh=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=wt(yu,t);return i.jsx(J1,{scope:t,forceMount:n,children:d.Children.map(r,l=>i.jsx(Nr,{present:n||s.open,children:i.jsx(uu,{asChild:!0,container:o,children:l})}))})};jh.displayName=yu;var Ks="DialogOverlay",Eh=d.forwardRef((e,t)=>{const n=Sh(Ks,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=wt(Ks,e.__scopeDialog);return s.modal?i.jsx(Nr,{present:r||s.open,children:i.jsx(tS,{...o,ref:t})}):null});Eh.displayName=Ks;var eS=So("DialogOverlay.RemoveScroll"),tS=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=wt(Ks,n);return i.jsx(hh,{as:eS,allowPinchZoom:!0,shards:[o.contentRef],children:i.jsx(le.div,{"data-state":wu(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),In="DialogContent",Ch=d.forwardRef((e,t)=>{const n=Sh(In,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=wt(In,e.__scopeDialog);return i.jsx(Nr,{present:r||s.open,children:s.modal?i.jsx(nS,{...o,ref:t}):i.jsx(rS,{...o,ref:t})})});Ch.displayName=In;var nS=d.forwardRef((e,t)=>{const n=wt(In,e.__scopeDialog),r=d.useRef(null),o=ut(t,n.contentRef,r);return d.useEffect(()=>{const s=r.current;if(s)return q1(s)},[]),i.jsx(Nh,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:J(e.onCloseAutoFocus,s=>{var l;s.preventDefault(),(l=n.triggerRef.current)==null||l.focus()}),onPointerDownOutside:J(e.onPointerDownOutside,s=>{const l=s.detail.originalEvent,a=l.button===0&&l.ctrlKey===!0;(l.button===2||a)&&s.preventDefault()}),onFocusOutside:J(e.onFocusOutside,s=>s.preventDefault())})}),rS=d.forwardRef((e,t)=>{const n=wt(In,e.__scopeDialog),r=d.useRef(!1),o=d.useRef(!1);return i.jsx(Nh,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var l,a;(l=e.onCloseAutoFocus)==null||l.call(e,s),s.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var u,c;(u=e.onInteractOutside)==null||u.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const l=s.target;((c=n.triggerRef.current)==null?void 0:c.contains(l))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),Nh=d.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...l}=e,a=wt(In,n),u=d.useRef(null),c=ut(t,u);return d1(),i.jsxs(i.Fragment,{children:[i.jsx(lh,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:i.jsx(au,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":wu(a.open),...l,ref:c,onDismiss:()=>a.onOpenChange(!1)})}),i.jsxs(i.Fragment,{children:[i.jsx(oS,{titleId:a.titleId}),i.jsx(lS,{contentRef:u,descriptionId:a.descriptionId})]})]})}),xu="DialogTitle",kh=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=wt(xu,n);return i.jsx(le.h2,{id:o.titleId,...r,ref:t})});kh.displayName=xu;var Th="DialogDescription",Ph=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=wt(Th,n);return i.jsx(le.p,{id:o.descriptionId,...r,ref:t})});Ph.displayName=Th;var _h="DialogClose",Rh=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=wt(_h,n);return i.jsx(le.button,{type:"button",...r,ref:t,onClick:J(e.onClick,()=>o.onOpenChange(!1))})});Rh.displayName=_h;function wu(e){return e?"open":"closed"}var bh="DialogTitleWarning",[gS,Ih]=Bx(bh,{contentName:In,titleName:xu,docsSlug:"dialog"}),oS=({titleId:e})=>{const t=Ih(bh),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return d.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},sS="DialogDescriptionWarning",lS=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Ih(sS).contentName}}.`;return d.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},iS=yh,aS=wh,uS=jh,Lh=Eh,zh=Ch,Dh=kh,Oh=Ph,cS=Rh;const Mh=iS,Ah=aS,dS=uS,Fh=d.forwardRef(({className:e,...t},n)=>i.jsx(Lh,{ref:n,className:B("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));Fh.displayName=Lh.displayName;const Su=d.forwardRef(({className:e,children:t,...n},r)=>i.jsxs(dS,{children:[i.jsx(Fh,{}),i.jsxs(zh,{ref:r,className:B("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-zinc-200 bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg dark:border-zinc-800 dark:bg-zinc-950",e),...n,children:[t,i.jsxs(cS,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-zinc-950 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-zinc-100 data-[state=open]:text-zinc-500 dark:ring-offset-zinc-950 dark:focus:ring-zinc-300 dark:data-[state=open]:bg-zinc-800 dark:data-[state=open]:text-zinc-400",children:[i.jsx(wm,{className:"h-4 w-4"}),i.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Su.displayName=zh.displayName;const ju=({className:e,...t})=>i.jsx("div",{className:B("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});ju.displayName="DialogHeader";const Eu=({className:e,...t})=>i.jsx("div",{className:B("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});Eu.displayName="DialogFooter";const Cu=d.forwardRef(({className:e,...t},n)=>i.jsx(Dh,{ref:n,className:B("text-lg font-semibold leading-none tracking-tight",e),...t}));Cu.displayName=Dh.displayName;const Nu=d.forwardRef(({className:e,...t},n)=>i.jsx(Oh,{ref:n,className:B("text-sm text-zinc-500 dark:text-zinc-400",e),...t}));Nu.displayName=Oh.displayName;function md(){var ku;const t=(ku=On().state)==null?void 0:ku.knowledgeBase,[n,r]=d.useState({}),[o,s]=d.useState(!1),l=["TDSQL-PG","TDSQL2.5","TDSQL3","CynosDB","TDSQL-Oracle"],[a,u]=d.useState([]),[c,m]=d.useState(!1),[p,g]=d.useState(""),[w,j]=d.useState(""),[y,x]=d.useState(!1),[h,f]=d.useState(!1),[v,S]=d.useState([{id:"",name:"",description:"",type:"",db_type:"",content:[""]}]),[E,T]=d.useState(""),[C,_]=d.useState([]),[z,O]=d.useState({page:1,limit:20,total:0}),[Q,F]=d.useState(!1),[q,A]=d.useState([]),[te,H]=d.useState(!1),[je,b]=d.useState(""),[D,N]=d.useState("keyword"),[M,U]=d.useState(!1),[Ee,tt]=d.useState(!1),{toast:de}=fl();d.useEffect(()=>{t&&(Lo(),Ge())},[t]);const Ge=async(k=1,R=20)=>{F(!0);try{const L=await Om(t.id,{page:k,limit:R});L.success&&L.data?(_(L.data.data),O(L.data.pagination)):de({title:"获取文档列表失败",description:L.error||"无法加载文档列表",variant:"destructive"})}catch{de({title:"获取文档列表异常",description:"网络连接异常,请稍后重试",variant:"destructive"})}finally{F(!1)}},hn=k=>{Ge(k,z.limit)},$h=k=>{Ge(1,k)},Uh=k=>{switch(k.toLowerCase()){case"completed":return i.jsx(ye,{variant:"secondary",children:"可用"});case"pending":return i.jsx(ye,{variant:"secondary",children:"处理中"});case"error":return i.jsx(ye,{variant:"destructive",children:"错误"});default:return i.jsx(ye,{variant:"outline",children:k})}},Bh=k=>{const R=typeof k=="string"?parseInt(k,10):k,L=R<9999999999;return new Date(L?R*1e3:R).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},Vh=k=>/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(k.trim()),Wh=async(k,R)=>{H(!0);try{const L=await Mm(t.id,k,{query:R,page:1,limit:100});if(L.success&&L.data){A(L.data.data);const Z=xl(L.data.data);u(Z),b(k),U(!1),de({title:"获取文档内容成功",description:`找到 ${Z.length} 条用例记录`})}else de({title:"获取文档内容失败",description:L.error||"无法获取文档分段",variant:"destructive"})}catch{de({title:"获取文档内容错误",description:"网络连接错误，请稍后重试",variant:"destructive"})}finally{H(!1)}},Qh=async k=>{H(!0);try{const R=C.map(Z=>Z.id);if(R.length===0){de({title:"搜索失败",description:"当前知识库中没有文档",variant:"destructive"});return}const L=await Dw(t.id,k,R);if(L.success&&L.data){const Z=[];L.data.forEach(vn=>{Z.push(...vn.data)});const Te=xl(Z);u(Te),A(Z),b(""),U(!1),Te.length>0?de({title:"搜索完成",description:`在 ${L.data.length} 个文档中找到 ${Te.length} 条用例记录`}):de({title:"搜索完成",description:`未找到包含 "${k}" 的相关用例`,variant:"destructive"})}else de({title:"搜索失败",description:L.error||"搜索过程中发生错误",variant:"destructive"})}catch{de({title:"搜索错误",description:"网络连接错误，请稍后重试",variant:"destructive"})}finally{H(!1)}},xl=k=>{const R=[];return k.forEach((L,Z)=>{try{const Te=L.content,vn=Te.split(`
`).filter(Pe=>Pe.trim());let ie=!1;for(const Pe of vn){const zo=Pe.match(/id:\s*([^;]+);\s*name:\s*([^;]+);\s*description:\s*([^;]+);\s*type:\s*([^;]+);\s*db_type:\s*([^;]+);\s*content:\s*(.+)/);if(zo){const[,Jh,ev,tv,nv,rv,Tu]=zo;let Do=[];try{const _r=Tu.trim();_r.startsWith("[")&&_r.endsWith("]")?Do=_r.slice(1,-1).split(",").map(wl=>wl.trim().replace(/^['"]|['"]$/g,"")).filter(wl=>wl.length>0):Do=[_r.replace(/^['"]|['"]$/g,"")]}catch{Do=[Tu.trim()]}const ov={id:Jh.trim(),name:ev.trim(),description:tv.trim(),type:nv.trim(),db_type:rv.trim(),content:Do,document_id:L.document_id};R.push(ov),ie=!0}}if(!ie&&Te.trim()){const Pe={id:`seg-${L.position}`,name:`分段-${L.position}`,description:L.answer||"从文档分段解析",type:L.status||"未知",db_type:L.keywords.join(", ")||"未知",content:[Te.trim()],document_id:L.document_id};R.push(Pe)}}catch(Te){console.error("解析分段内容失败:",Te,L);const vn={id:`err-${L.position}`,name:`分段-${L.position}`,description:L.answer||"解析失败",type:"解析错误",db_type:"未知",content:[L.content],document_id:L.document_id};R.push(vn)}}),R},Lo=async()=>{if(!p.trim()){de({title:"搜索提示",description:"请输入搜索关键词或文档ID",variant:"destructive"});return}m(!0),u([]);try{if(Vh(p))N("document"),await Wh(p.trim());else{N("keyword");const k=await bw({query:p,knowledge_base_id:t==null?void 0:t.id});k.success&&k.data&&u(k.data),await Qh(p)}}catch{de({title:"搜索错误",description:"网络连接错误，请稍后重试",variant:"destructive"})}finally{m(!1)}},Hh=async()=>{try{r({}),s(!1);const k=v.filter(ie=>ie.id.trim()&&ie.name.trim()&&ie.description.trim()&&ie.type.trim()&&ie.db_type.trim()&&ie.content.some(Pe=>Pe.trim()));if(k.length===0)throw new Error("请至少填写一行完整的用例数据");const R={};if(v.forEach((ie,Pe)=>{ie.id.trim()||(R[`id-${Pe}`]="ID不能为空"),ie.name.trim()||(R[`name-${Pe}`]="名称不能为空"),ie.description.trim()||(R[`description-${Pe}`]="描述不能为空"),ie.type.trim()||(R[`type-${Pe}`]="类型不能为空"),ie.db_type.trim()||(R[`db_type-${Pe}`]="数据库类型不能为空"),ie.content.some(zo=>zo.trim())||(R[`content-${Pe}`]="内容不能为空")}),Object.keys(R).length>0)throw r(R),new Error("请填写所有必填字段");x(!0);const L=k.map(ie=>({...ie,content:ie.content.filter(Pe=>Pe.trim())})),Z=E.trim()?`${E.trim()}.csv`:`usecases_${new Date().toISOString().slice(0,19).replace(/[-T:]/g,"")}.csv`,Te=Ow(L),vn=Am(Te,Z,"text/csv");if(t){const ie=await Lw(vn,t.id);ie.success?(de({title:"上传成功",description:ie.message||"用例数据已成功添加到知识库"}),S([{id:"",name:"",description:"",type:"",db_type:"",content:[""]}]),Lo(),Ge()):de({title:"上传失败",description:ie.message||"上传过程中发生错误",variant:"destructive"})}}catch(k){de({title:"添加失败",description:k instanceof Error?k.message:"请检查输入数据",variant:"destructive"})}finally{x(!1)}},Kh=async()=>{if(!w.trim()){de({title:"删除失败",description:"请输入要删除的用例名称",variant:"destructive"});return}try{const k=a.filter(R=>R.name!==w);u(k),de({title:"删除成功",description:`用例 "${w}" 已被删除`}),j(""),f(!1)}catch{de({title:"删除失败",description:"删除过程中发生错误",variant:"destructive"})}},Gh=()=>{S([...v,{id:"",name:"",description:"",type:"",db_type:"",content:[""]}])},Xh=k=>{if(v.length>1){const R=[...v];R.splice(k,1),S(R)}},Pr=(k,R,L)=>{const Z=[...v];Z[k]={...Z[k],[R]:L},S(Z)},Yh=(k,R,L)=>{const Z=[...v],Te=[...Z[k].content];Te[R]=L,Z[k].content=Te,S(Z)},qh=k=>{const R=[...v];R[k].content.push(""),S(R)},Zh=(k,R)=>{if(v[k].content.length>1){const L=[...v];L[k].content.splice(R,1),S(L)}};return t?i.jsxs("div",{className:"space-y-6",children:[i.jsx(we,{children:i.jsxs(Ie,{children:[i.jsxs(Oe,{className:"flex items-center space-x-2",children:[i.jsx(zt,{className:"w-6 h-6"}),i.jsx("span",{children:"用例经验知识库管理"})]}),i.jsxs(Be,{children:["当前知识库: ",t.name," (ID: ",t.id,")"]}),i.jsxs(Ie,{children:[i.jsxs(Oe,{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(zt,{className:"w-5 h-5"}),i.jsx("span",{children:"用例文档列表"})]}),i.jsxs(X,{variant:"outline",size:"sm",onClick:()=>Ge(),disabled:Q,children:[i.jsx(ym,{className:`w-4 h-4 mr-2 ${Q?"animate-spin":""}`}),"刷新列表"]})]}),i.jsxs(Be,{children:["当前知识库中的所有文档，共 ",C.length," 个文档"]})]}),i.jsx(Me,{children:Q?i.jsx("div",{className:"flex justify-center items-center h-40",children:i.jsx("div",{className:"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"})}):i.jsx(i.Fragment,{children:C.length>0?i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"border rounded-lg",children:i.jsxs(ur,{children:[i.jsx(cr,{children:i.jsxs(mt,{children:[i.jsx(K,{children:"文档ID"}),i.jsx(K,{children:"文档名称"}),i.jsx(K,{children:"类型"}),i.jsx(K,{children:"状态"}),i.jsx(K,{children:"创建时间"}),i.jsx(K,{children:"字数"})]})}),i.jsx(dr,{children:C.map(k=>{var R;return i.jsxs(mt,{children:[i.jsx(G,{className:"font-medium",children:k.id}),i.jsx(G,{className:"font-medium",children:k.name||"未命名文档"}),i.jsx(G,{children:i.jsx(ye,{variant:"outline",children:k.type})}),i.jsx(G,{children:Uh(k.status)}),i.jsx(G,{className:"text-sm",children:Bh(k.created_at)}),i.jsx(G,{className:"text-right",children:((R=k.word_count)==null?void 0:R.toLocaleString())||0})]},k.id)})})]})}),i.jsxs("div",{className:"flex items-center justify-between mt-4",children:[i.jsxs("div",{className:"text-sm text-gray-500",children:["显示 ",C.length," 条中的第 ",z.page," 页，共 ",Math.ceil(z.total/z.limit)," 页"]}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(X,{variant:"outline",size:"sm",onClick:()=>hn(z.page-1),disabled:z.page<=1,children:"上一页"}),i.jsx("span",{className:"text-sm",children:z.page}),i.jsx(X,{variant:"outline",size:"sm",onClick:()=>hn(z.page+1),disabled:z.page>=Math.ceil(z.total/z.limit),children:"下一页"}),i.jsxs("select",{value:z.limit,onChange:k=>$h(Number(k.target.value)),className:"border rounded p-1 text-sm",children:[i.jsx("option",{value:10,children:"10条/页"}),i.jsx("option",{value:20,children:"20条/页"}),i.jsx("option",{value:50,children:"50条/页"}),i.jsx("option",{value:100,children:"100条/页"})]})]})]})]}):i.jsxs("div",{className:"flex flex-col items-center justify-center py-12 space-y-4 text-center",children:[i.jsx(zt,{className:"w-16 h-16 text-gray-300"}),i.jsx("h3",{className:"text-lg font-medium",children:"暂无文档"}),i.jsx("p",{className:"text-sm text-gray-500",children:"当前知识库中还没有任何文档，请上传用例数据。"})]})})})]})}),i.jsxs(sh,{defaultValue:"search",className:"space-y-6",children:[i.jsxs(gu,{className:"grid w-full grid-cols-3",children:[i.jsxs(Nn,{value:"search",className:"flex items-center space-x-2",children:[i.jsx(hl,{className:"w-4 h-4"}),i.jsx("span",{children:"查看用例"})]}),i.jsxs(Nn,{value:"add",className:"flex items-center space-x-2",children:[i.jsx(ir,{className:"w-4 h-4"}),i.jsx("span",{children:"新增用例"})]}),i.jsxs(Nn,{value:"delete",className:"flex items-center space-x-2",children:[i.jsx(er,{className:"w-4 h-4"}),i.jsx("span",{children:"删除用例"})]})]}),i.jsx(kn,{value:"search",children:i.jsxs(we,{children:[i.jsxs(Ie,{children:[i.jsx(Oe,{children:"搜索用例与文档内容"}),i.jsx(Be,{children:"输入关键词搜索用例经验数据和文档内容，或输入文档ID查看具体文档分段"})]}),i.jsxs(Me,{className:"space-y-4",children:[i.jsxs("div",{className:"flex items-end space-x-4",children:[i.jsxs("div",{className:"flex-1",children:[i.jsx(De,{htmlFor:"search-input",children:"搜索关键词或文档ID"}),i.jsx(nt,{id:"search-input",placeholder:"输入关键词搜索所有文档，或输入文档ID查看具体内容...",value:p,onChange:k=>g(k.target.value),onKeyPress:k=>k.key==="Enter"&&Lo()}),i.jsx("p",{className:"text-xs text-slate-500 mt-1",children:"提示：输入 UUID 格式的文档ID可查看该文档的所有分段内容"})]}),i.jsx(X,{onClick:Lo,disabled:c||te,children:c||te?"搜索中...":"搜索"})]}),D&&a.length>0&&i.jsxs(ht,{className:"mt-4",children:[i.jsx(Zc,{className:"h-4 w-4"}),i.jsxs(vt,{children:[D==="document"?`正在显示文档 ${je} 解析出的用例数据`:`正在显示关键词 "${p}" 搜索到的用例数据`,q.length>0&&i.jsx("div",{className:"mt-2 space-x-2",children:i.jsx(X,{variant:"link",size:"sm",onClick:()=>tt(!Ee),className:"p-0 h-auto",children:Ee?"隐藏原始分段":"查看原始分段"})})]})]}),Ee&&q.length>0&&i.jsxs("div",{className:"space-y-4 mt-4 p-4 bg-gray-50 rounded-lg border",children:[i.jsx("div",{className:"flex items-center justify-between",children:i.jsxs("h4",{className:"text-md font-medium flex items-center space-x-2",children:[i.jsx(O0,{className:"w-4 h-4"}),i.jsx("span",{children:"原始文档分段（调试）"}),i.jsxs(ye,{variant:"outline",children:[q.length," 个分段"]})]})}),i.jsxs("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:[q.slice(0,3).map((k,R)=>i.jsxs("div",{className:"p-3 bg-white rounded border text-xs",children:[i.jsxs("div",{className:"font-mono text-gray-600 mb-2",children:["分段 ",k.position," (ID: ",k.id,")"]}),i.jsx("div",{className:"text-gray-800 whitespace-pre-wrap line-clamp-4",children:k.content})]},k.id)),q.length>3&&i.jsxs("div",{className:"text-center text-gray-500 text-sm",children:["还有 ",q.length-3," 个分段..."]})]})]}),a.length>0&&i.jsx("div",{className:"space-y-6",children:q.length>0?i.jsx("div",{className:"space-y-4",children:i.jsxs("div",{className:"space-y-2",children:[i.jsxs("h3",{className:"text-lg font-medium flex items-center space-x-2",children:[i.jsx(Zc,{className:"w-5 h-5"}),i.jsx("span",{children:"文档内容"}),i.jsxs(ye,{variant:"secondary",children:[q.length," 个分段"]})]}),i.jsx("div",{className:"border rounded-lg",children:i.jsxs(ur,{children:[i.jsx(cr,{children:i.jsxs(mt,{children:[i.jsx(K,{children:"文档ID"}),i.jsx(K,{children:"ID"}),i.jsx(K,{children:"名称"}),i.jsx(K,{children:"描述"}),i.jsx(K,{children:"类型"}),i.jsx(K,{children:"数据库类型"}),i.jsx(K,{children:"内容"})]})}),i.jsx(dr,{children:xl(q).map((k,R)=>i.jsxs(mt,{children:[i.jsx(G,{className:"font-mono text-sm",children:k.document_id||"未知"}),i.jsx(G,{className:"font-mono text-sm",children:k.id}),i.jsx(G,{className:"font-medium",children:k.name}),i.jsx(G,{className:"max-w-xs",children:i.jsx("div",{className:"truncate",title:k.description,children:k.description})}),i.jsx(G,{children:i.jsx(ye,{variant:"outline",children:k.type})}),i.jsx(G,{children:i.jsx(ye,{variant:"secondary",children:k.db_type})}),i.jsx(G,{className:"max-w-xs",children:i.jsx("div",{className:"space-y-1",children:Array.isArray(k.content)?k.content.map((L,Z)=>i.jsxs("div",{className:"text-sm text-slate-600 truncate",children:["• ",L]},Z)):i.jsx("div",{className:"text-sm text-slate-600 truncate",children:k.content})})})]},`${k.document_id}-${R}`))})]})})]})}):i.jsx("div",{className:"border rounded-lg",children:i.jsxs(ur,{children:[i.jsx(cr,{children:i.jsxs(mt,{children:[i.jsx(K,{children:"ID"}),i.jsx(K,{children:"名称"}),i.jsx(K,{children:"描述"}),i.jsx(K,{children:"类型"}),i.jsx(K,{children:"数据库类型"}),i.jsx(K,{children:"内容"})]})}),i.jsx(dr,{children:a.map(k=>i.jsxs(mt,{children:[i.jsx(G,{className:"font-mono text-sm",children:k.id}),i.jsx(G,{className:"font-medium",children:k.name}),i.jsx(G,{className:"max-w-xs",children:i.jsx("div",{className:"truncate",title:k.description,children:k.description})}),i.jsx(G,{children:i.jsx(ye,{variant:"outline",children:k.type})}),i.jsx(G,{children:i.jsx(ye,{variant:"secondary",children:k.db_type})}),i.jsx(G,{className:"max-w-xs",children:i.jsx("div",{className:"space-y-1",children:Array.isArray(k.content)?k.content.map((R,L)=>i.jsxs("div",{className:"text-sm text-slate-600 truncate",children:["• ",R]},L)):i.jsx("div",{className:"text-sm text-slate-600 truncate",children:k.content})})})]},k.id))})]})})}),a.length===0&&!c&&!te&&p&&i.jsxs("div",{className:"text-center py-8 text-slate-500",children:[i.jsx(zt,{className:"w-12 h-12 mx-auto mb-4 text-slate-300"}),i.jsx("p",{children:"暂无搜索结果，请尝试其他关键词或文档ID"}),i.jsx("p",{className:"text-xs mt-2",children:"提示：可以从文档列表中复制文档ID进行精确查询"})]})]})]})}),i.jsx(kn,{value:"add",children:i.jsxs(we,{children:[i.jsxs(Ie,{children:[i.jsx(Oe,{children:"新增用例数据"}),i.jsx(Be,{children:"填写以下表单添加新的用例数据"})]}),i.jsxs(Me,{className:"space-y-6",children:[o&&i.jsxs(ht,{className:"mb-4",children:[i.jsx(Hs,{className:"h-4 w-4"}),i.jsxs(vt,{children:[i.jsx("strong",{children:"提交成功!"})," 用例数据已成功添加到知识库"]})]}),i.jsxs("div",{children:[i.jsx(De,{htmlFor:"file-name",children:"用例数据文件名（可选）"}),i.jsx(nt,{id:"file-name",placeholder:"输入自定义文件名（不包括扩展名）",value:E,onChange:k=>T(k.target.value)}),i.jsxs("p",{className:"text-red-500 text-xs mt-1",children:["建议格式：",i.jsx("span",{className:"font-medium",children:"产品名_描述性名称"})]}),i.jsx("p",{className:"text-xs text-slate-500 mt-1",children:E.trim()?`文件将保存为: ${E.trim()}.csv`:"不填写将自动生成文件名 (如: usecases_202506241230.csv)"})]}),i.jsx("div",{className:"space-y-4",children:v.map((k,R)=>i.jsxs(we,{className:"border rounded-lg p-4 bg-white shadow-sm",children:[i.jsxs("div",{className:"flex justify-between items-center mb-4",children:[i.jsxs("h3",{className:"text-lg font-medium",children:["用例 #",R+1]}),i.jsx(X,{variant:"destructive",size:"icon",onClick:()=>Xh(R),disabled:v.length<=1,children:i.jsx(er,{className:"w-4 h-4"})})]}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx(De,{htmlFor:`id-${R}`,children:"用例ID *"}),i.jsx(nt,{id:`id-${R}`,placeholder:"唯一标识符",value:k.id,onChange:L=>Pr(R,"id",L.target.value),className:n[`id-${R}`]?"border-red-500":""}),n[`id-${R}`]&&i.jsx("p",{className:"text-red-500 text-xs mt-1",children:n[`id-${R}`]})]}),i.jsxs("div",{children:[i.jsx(De,{htmlFor:`name-${R}`,children:"用例名称 *"}),i.jsx(nt,{id:`name-${R}`,placeholder:"用例名称",value:k.name,onChange:L=>Pr(R,"name",L.target.value),className:n[`name-${R}`]?"border-red-500":""}),n[`name-${R}`]&&i.jsx("p",{className:"text-red-500 text-xs mt-1",children:n[`name-${R}`]})]}),i.jsxs("div",{children:[i.jsx(De,{htmlFor:`description-${R}`,children:"用例描述 *"}),i.jsx(hu,{id:`description-${R}`,placeholder:"用例描述",value:k.description,onChange:L=>Pr(R,"description",L.target.value),rows:3,className:n[`description-${R}`]?"border-red-500":""}),n[`description-${R}`]&&i.jsx("p",{className:"text-red-500 text-xs mt-1",children:n[`description-${R}`]})]})]}),i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx(De,{htmlFor:`type-${R}`,children:"用例类型 *"}),i.jsx(nt,{id:`type-${R}`,placeholder:"用例类型",value:k.type,onChange:L=>Pr(R,"type",L.target.value),className:n[`type-${R}`]?"border-red-500":""}),n[`type-${R}`]&&i.jsx("p",{className:"text-red-500 text-xs mt-1",children:n[`type-${R}`]})]}),i.jsxs("div",{children:[i.jsx(De,{htmlFor:`db_type-${R}`,children:"数据库类型 *"}),i.jsxs("select",{id:`db_type-${R}`,value:k.db_type,onChange:L=>Pr(R,"db_type",L.target.value),className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md",children:[i.jsx("option",{value:"",children:"请选择数据库类型"}),l.map(L=>i.jsx("option",{value:L,children:L},L))]}),n[`db_type-${R}`]&&i.jsx("p",{className:"text-red-500 text-xs mt-1",children:n[`db_type-${R}`]})]}),i.jsxs("div",{children:[i.jsx(De,{children:"用例列表 *"}),i.jsxs("div",{className:"space-y-2",children:[k.content.map((L,Z)=>i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(nt,{placeholder:`用例 ${Z+1}`,value:L,onChange:Te=>Yh(R,Z,Te.target.value)}),i.jsx(X,{variant:"destructive",size:"icon",onClick:()=>Zh(R,Z),disabled:k.content.length<=1,children:i.jsx(er,{className:"w-4 h-4"})})]},Z)),i.jsxs(X,{variant:"outline",size:"sm",onClick:()=>qh(R),className:"mt-1",children:[i.jsx(ir,{className:"w-4 h-4 mr-1"})," 添加用例"]})]}),n[`content-${R}`]&&i.jsx("p",{className:"text-red-500 text-xs mt-1",children:n[`content-${R}`]})]})]})]})]},R))}),i.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 pt-4",children:[i.jsxs(X,{variant:"outline",onClick:Gh,className:"flex items-center space-x-2",children:[i.jsx(ir,{className:"w-4 h-4"}),i.jsx("span",{children:"添加新用例"})]}),i.jsx(X,{onClick:Hh,disabled:y,style:{backgroundColor:"white",color:"black"},className:"flex items-center space-x-2",children:y?i.jsxs("div",{className:"flex items-center",children:[i.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i.jsx("span",{style:{fontWeight:"bold"},children:"上传中..."})]}):i.jsxs(i.Fragment,{children:[i.jsx(xm,{className:"w-5 h-5"}),i.jsx("span",{children:"提交所有用例"})]})})]}),i.jsxs(ht,{className:"mt-6",children:[i.jsx(Hs,{className:"h-4 w-4"}),i.jsx(vt,{children:i.jsxs("div",{className:"space-y-2",children:[i.jsx("p",{children:i.jsx("strong",{children:"填写说明："})}),i.jsxs("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[i.jsxs("li",{children:["所有带 ",i.jsx("span",{className:"text-red-500",children:"*"})," 的字段为必填项"]}),i.jsx("li",{children:"用例ID: 暂时没用到，可以写1,2,3..即可"}),i.jsx("li",{children:"用例名称: 对应智研的测试设计的测试点(‼️)"}),i.jsx("li",{children:"用例描述: 对应智研的用例描述(‼️)"}),i.jsx("li",{children:"用例类型: 暂时没用到,可以填写dml、ddl、table等"}),i.jsx("li",{children:"数据库类型: 根据下拉框填写对应产品即可"}),i.jsx("li",{children:"用例列表: 可以理解为对应智研的用例标题,一个测试点对应多个测试用例"})]})]})})]})]})]})}),i.jsx(kn,{value:"delete",children:i.jsxs(we,{children:[i.jsxs(Ie,{children:[i.jsx(Oe,{children:"删除用例"}),i.jsx(Be,{children:"输入要删除的用例名称"})]}),i.jsxs(Me,{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx(De,{htmlFor:"delete-input",children:"用例名称"}),i.jsx(nt,{id:"delete-input",placeholder:"输入要删除的用例名称...",value:w,onChange:k=>j(k.target.value)})]}),i.jsxs(Mh,{open:h,onOpenChange:f,children:[i.jsx(Ah,{asChild:!0,children:i.jsxs(X,{variant:"destructive",disabled:!w.trim(),className:"flex items-center space-x-2",children:[i.jsx(er,{className:"w-4 h-4"}),i.jsx("span",{children:"删除用例"})]})}),i.jsxs(Su,{children:[i.jsxs(ju,{children:[i.jsx(Cu,{children:"确认删除"}),i.jsxs(Nu,{children:['您确定要删除用例 "',w,'" 吗？此操作不可撤销。']})]}),i.jsxs(Eu,{children:[i.jsx(X,{variant:"outline",onClick:()=>f(!1),children:"取消"}),i.jsx(X,{variant:"destructive",onClick:Kh,children:"确认删除"})]})]})]}),i.jsxs(ht,{variant:"destructive",children:[i.jsx(jo,{className:"h-4 w-4"}),i.jsxs(vt,{children:[i.jsx("strong",{children:"注意："}),"删除操作将永久移除用例数据，请谨慎操作。"]})]})]})]})})]})]}):i.jsxs(ht,{variant:"destructive",children:[i.jsx(jo,{className:"h-4 w-4"}),i.jsxs(vt,{children:["请先选择一个知识库。",i.jsx("a",{href:"/",className:"underline",children:"返回知识库选择"})]})]})}function hd(){var D;const t=(D=On().state)==null?void 0:D.knowledgeBase,[n,r]=d.useState([]),[o,s]=d.useState(!1),[l,a]=d.useState(""),[u,c]=d.useState(""),[m,p]=d.useState(""),[g,w]=d.useState(!1),[j,y]=d.useState(!1),{toast:x}=fl(),[h,f]=d.useState(""),[v,S]=d.useState([]),[E,T]=d.useState({page:1,limit:20,total:0}),[C,_]=d.useState(!1);d.useEffect(()=>{t&&(O(),z())},[t]);const z=async(N=1,M=20)=>{_(!0);try{const U=await Om(t.id,{page:N,limit:M});U.success&&U.data?(S(U.data.data),T(U.data.pagination)):x({title:"获取文档列表失败",description:U.error||"无法加载文档列表",variant:"destructive"})}catch{x({title:"获取文档列表异常",description:"网络连接异常,请稍后重试",variant:"destructive"})}finally{_(!1)}},O=async()=>{s(!0);try{const N=await Iw({query:l,knowledge_base_id:t==null?void 0:t.id});N.success&&N.data?(r(N.data),x({title:"搜索完成",description:`找到 ${N.data.length} 条SQL记录`})):x({title:"搜索失败",description:N.error||"搜索过程中发生错误",variant:"destructive"})}catch{x({title:"搜索错误",description:"网络连接错误，请稍后重试",variant:"destructive"})}finally{s(!1)}},Q=async()=>{if(!u.trim()){x({title:"添加失败",description:"请输入SQL内容",variant:"destructive"});return}try{w(!0);const N=h.trim()?`${encodeURIComponent(h.trim())}.txt`:`usecases_${new Date().toISOString().slice(0,19).replace(/[-T:]/g,"")}.txt`,M=Am(u,N,"text/plain");if(t){const U=await zw(M,t.id);U.success?(x({title:"上传成功",description:U.message||"SQL数据已成功添加到知识库"}),c(""),O(),z()):x({title:"上传失败",description:U.message||"上传过程中发生错误",variant:"destructive"})}}catch(N){x({title:"添加失败",description:N instanceof Error?N.message:"上传过程中发生错误",variant:"destructive"})}finally{w(!1)}},F=async()=>{if(!m.trim()){x({title:"删除失败",description:"请输入要删除的SQL名称",variant:"destructive"});return}try{const N=n.filter(M=>M.name!==m);r(N),x({title:"删除成功",description:`SQL "${m}" 已被删除`}),p(""),y(!1),z()}catch{x({title:"删除失败",description:"删除过程中发生错误",variant:"destructive"})}},q=async N=>{try{await navigator.clipboard.writeText(N),x({title:"复制成功",description:"SQL语句已复制到剪贴板"})}catch{x({title:"复制失败",description:"无法复制到剪贴板",variant:"destructive"})}},A=`fqs查询,agg函数在having中
:ident:
\`\`\`sql
select * from fqs_shard_1 group by id having count(*) > 1000;
select max(max) from (select max(dt) from fqs_multi_shard_1 group by id,deptno,ts having avg(age) = 10);
select max(avg) from (select avg(age) from fqs_list_1 group by address,dt having max(dt) > '2025-10-01');
\`\`\`

TDSQL-PG:复杂聚合查询,窗口函数与分组
:ident:
\`\`\`sql
select 
  id,
  name,
  row_number() over (partition by dept_id order by salary desc) as rank
from employees 
where status = 'active';

select 
  dept_id,
  avg(salary) as avg_salary,
  count(*) as emp_count
from employees 
group by dept_id 
having count(*) > 5;
\`\`\``;if(!t)return i.jsxs(ht,{variant:"destructive",children:[i.jsx(jo,{className:"h-4 w-4"}),i.jsxs(vt,{children:["请先选择一个知识库。",i.jsx("a",{href:"/",className:"underline",children:"返回知识库选择"})]})]});const te=N=>{const M=typeof N=="string"?parseInt(N,10):N,U=M<9999999999;return new Date(U?M*1e3:M).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},H=N=>{switch(N.toLowerCase()){case"completed":return i.jsx(ye,{variant:"secondary",children:"可用"});case"pending":return i.jsx(ye,{variant:"secondary",children:"处理中"});case"error":return i.jsx(ye,{variant:"destructive",children:"错误"});default:return i.jsx(ye,{variant:"outline",children:N})}},je=N=>{z(N,E.limit)},b=N=>{z(1,N)};return i.jsxs("div",{className:"space-y-6",children:[i.jsx(we,{children:i.jsxs(Ie,{children:[i.jsxs(Oe,{className:"flex items-center space-x-2",children:[i.jsx(ia,{className:"w-6 h-6"}),i.jsx("span",{children:"SQL经验知识库管理"})]}),i.jsxs(Be,{children:["当前知识库: ",t.name," (ID: ",t.id,")"]}),i.jsxs(Ie,{children:[i.jsxs(Oe,{className:"flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(zt,{className:"w-5 h-5"}),i.jsx("span",{children:"SQL文档列表"})]}),i.jsxs(X,{variant:"outline",size:"sm",onClick:()=>z(),disabled:C,children:[i.jsx(ym,{className:`w-4 h-4 mr-2 ${C?"animate-spin":""}`}),"刷新列表"]})]}),i.jsxs(Be,{children:["当前知识库中的所有文档，共 ",v.length," 个文档"]})]}),i.jsx(Me,{children:C?i.jsx("div",{className:"flex justify-center items-center h-40",children:i.jsx("div",{className:"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"})}):i.jsx(i.Fragment,{children:v.length>0?i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"border rounded-lg",children:i.jsxs(ur,{children:[i.jsx(cr,{children:i.jsxs(mt,{children:[i.jsx(K,{children:"文档ID"}),i.jsx(K,{children:"文档名称"}),i.jsx(K,{children:"类型"}),i.jsx(K,{children:"状态"}),i.jsx(K,{children:"创建时间"}),i.jsx(K,{children:"字数"})]})}),i.jsx(dr,{children:v.map(N=>{var M;return i.jsxs(mt,{children:[i.jsx(G,{className:"font-medium",children:N.id}),i.jsx(G,{className:"font-medium",children:N.name||"未命名文档"}),i.jsx(G,{children:i.jsx(ye,{variant:"outline",children:N.type})}),i.jsx(G,{children:H(N.status)}),i.jsx(G,{className:"text-sm",children:te(N.created_at)}),i.jsx(G,{className:"text-right",children:((M=N.word_count)==null?void 0:M.toLocaleString())||0})]},N.id)})})]})}),i.jsxs("div",{className:"flex items-center justify-between mt-4",children:[i.jsxs("div",{className:"text-sm text-gray-500",children:["显示 ",v.length," 条中的第 ",E.page," 页，共 ",Math.ceil(E.total/E.limit)," 页"]}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx(X,{variant:"outline",size:"sm",onClick:()=>je(E.page-1),disabled:E.page<=1,children:"上一页"}),i.jsx("span",{className:"text-sm",children:E.page}),i.jsx(X,{variant:"outline",size:"sm",onClick:()=>je(E.page+1),disabled:E.page>=Math.ceil(E.total/E.limit),children:"下一页"}),i.jsxs("select",{value:E.limit,onChange:N=>b(Number(N.target.value)),className:"border rounded p-1 text-sm",children:[i.jsx("option",{value:10,children:"10条/页"}),i.jsx("option",{value:20,children:"20条/页"}),i.jsx("option",{value:50,children:"50条/页"}),i.jsx("option",{value:100,children:"100条/页"})]})]})]})]}):i.jsxs("div",{className:"flex flex-col items-center justify-center py-12 space-y-4 text-center",children:[i.jsx(zt,{className:"w-16 h-16 text-gray-300"}),i.jsx("h3",{className:"text-lg font-medium",children:"暂无文档"}),i.jsx("p",{className:"text-sm text-gray-500",children:"当前知识库中还没有任何文档，请上传SQL数据。"}),i.jsx(X,{variant:"outline",onClick:()=>{var N;return(N=document.querySelector('button[data-state="active"]'))==null?void 0:N.dispatchEvent(new MouseEvent("click"))},children:"前往上传"})]})})})]})}),i.jsxs(sh,{defaultValue:"search",className:"space-y-6",children:[i.jsxs(gu,{className:"grid w-full grid-cols-3",children:[i.jsxs(Nn,{value:"search",className:"flex items-center space-x-2",children:[i.jsx(hl,{className:"w-4 h-4"}),i.jsx("span",{children:"查看SQL"})]}),i.jsxs(Nn,{value:"add",className:"flex items-center space-x-2",children:[i.jsx(ir,{className:"w-4 h-4"}),i.jsx("span",{children:"新增SQL"})]}),i.jsxs(Nn,{value:"delete",className:"flex items-center space-x-2",children:[i.jsx(er,{className:"w-4 h-4"}),i.jsx("span",{children:"删除SQL"})]})]}),i.jsx(kn,{value:"search",children:i.jsxs(we,{children:[i.jsxs(Ie,{children:[i.jsx(Oe,{children:"搜索SQL"}),i.jsx(Be,{children:"输入关键词搜索SQL经验数据"})]}),i.jsxs(Me,{className:"space-y-4",children:[i.jsxs("div",{className:"flex items-end space-x-4",children:[i.jsxs("div",{className:"flex-1",children:[i.jsx(De,{htmlFor:"search-input",children:"搜索关键词"}),i.jsx(nt,{id:"search-input",placeholder:"输入SQL名称、描述或SQL语句...",value:l,onChange:N=>a(N.target.value),onKeyPress:N=>N.key==="Enter"&&O()})]}),i.jsx(X,{onClick:O,disabled:o,children:o?"搜索中...":"搜索"})]}),n.length>0&&i.jsx("div",{className:"border rounded-lg",children:i.jsxs(ur,{children:[i.jsx(cr,{children:i.jsxs(mt,{children:[i.jsx(K,{children:"ID"}),i.jsx(K,{children:"名称"}),i.jsx(K,{children:"描述"}),i.jsx(K,{children:"SQL内容"}),i.jsx(K,{children:"标签"}),i.jsx(K,{children:"操作"})]})}),i.jsx(dr,{children:n.map(N=>{var M;return i.jsxs(mt,{children:[i.jsx(G,{className:"font-mono text-sm",children:N.id}),i.jsx(G,{className:"font-medium",children:N.name}),i.jsx(G,{className:"max-w-xs",children:i.jsx("div",{className:"truncate",title:N.description,children:N.description})}),i.jsx(G,{className:"max-w-md",children:i.jsx("div",{className:"relative",children:i.jsx("pre",{className:"bg-slate-50 p-3 rounded text-xs font-mono overflow-x-auto max-h-32 border",children:i.jsx("code",{children:N.sql_content})})})}),i.jsx(G,{children:i.jsx("div",{className:"flex flex-wrap gap-1",children:(M=N.tags)==null?void 0:M.map((U,Ee)=>i.jsx(ye,{variant:"outline",className:"text-xs",children:U},Ee))})}),i.jsx(G,{children:i.jsxs(X,{size:"sm",variant:"outline",onClick:()=>q(N.sql_content),className:"flex items-center space-x-1",children:[i.jsx(D0,{className:"w-3 h-3"}),i.jsx("span",{children:"复制"})]})})]},N.id)})})]})}),n.length===0&&!o&&i.jsx("div",{className:"text-center py-8 text-slate-500",children:"暂无搜索结果，请尝试其他关键词"})]})]})}),i.jsx(kn,{value:"add",children:i.jsxs(we,{children:[i.jsxs(Ie,{children:[i.jsx(Oe,{children:"新增SQL数据"}),i.jsx(Be,{children:"输入SQL内容，系统将自动转换为TXT格式上传"})]}),i.jsxs(Me,{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx(De,{htmlFor:"file-name",children:"经验SQL文件名（可选）"}),i.jsx(nt,{id:"file-name",placeholder:"输入自定义文件名（不包括扩展名）",value:h,onChange:N=>f(N.target.value)}),i.jsxs("p",{className:"text-red-500 text-xs mt-1",children:["建议格式：",i.jsx("span",{className:"font-medium",children:"产品名_描述性名称"})]}),i.jsx("p",{className:"text-xs text-slate-500 mt-1",children:h.trim()?`文件将保存为: ${h.trim()}.txt`:"不填写将自动生成文件名 (如: usecases_202506241230.txt)"})]}),i.jsxs("div",{children:[i.jsx(De,{htmlFor:"text-input",children:"SQL内容"}),i.jsx(hu,{id:"text-input",placeholder:A,value:u,onChange:N=>c(N.target.value),rows:20,className:"font-mono text-sm"}),i.jsx("div",{className:"text-sm text-slate-500 mt-2",children:"支持格式：[产品:]名称,描述 + :ident: + SQL代码块"})]}),i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsxs(X,{onClick:Q,disabled:!u.trim()||g,className:"flex items-center space-x-2",children:[i.jsx(xm,{className:"w-4 h-4"}),i.jsx("span",{children:g?"上传中...":"添加SQL"})]}),i.jsx(X,{variant:"outline",onClick:()=>c(A),children:"使用示例数据"})]}),i.jsxs(ht,{children:[i.jsx(Hs,{className:"h-4 w-4"}),i.jsxs(vt,{children:[i.jsx("strong",{children:"文本格式要求："}),i.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1 text-sm",children:[i.jsx("li",{children:'每个SQL条目以"[产品:]名称,描述"开始,如果这个SQL是产品独有的,就需要加上产品'}),i.jsx("li",{children:'使用":ident:"作为分隔符'}),i.jsx("li",{children:"SQL代码可以用```sql代码块包装"}),i.jsx("li",{children:"支持多个SQL条目，每个条目独立分隔"}),i.jsx("li",{children:"系统将自动转换为TXT格式上传到知识库"})]})]})]}),i.jsxs(we,{className:"bg-slate-50",children:[i.jsx(Ie,{children:i.jsx(Oe,{className:"text-sm",children:"格式示例"})}),i.jsx(Me,{children:i.jsx("pre",{className:"text-xs font-mono overflow-x-auto",children:"fqs查询,agg函数在having中\n:ident:\n```sql\nselect * from fqs_shard_1 group by id having count(*) > 1000;\n```\n\nTDSQL-PG:复杂查询,嵌套查询示例\n:ident:\n```sql\nselect max(max) from (select max(dt) from table group by id);\n```"})})]})]})]})}),i.jsx(kn,{value:"delete",children:i.jsxs(we,{children:[i.jsxs(Ie,{children:[i.jsx(Oe,{children:"删除SQL"}),i.jsx(Be,{children:"输入要删除的SQL名称"})]}),i.jsxs(Me,{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx(De,{htmlFor:"delete-input",children:"SQL名称"}),i.jsx(nt,{id:"delete-input",placeholder:"输入要删除的SQL名称...",value:m,onChange:N=>p(N.target.value)})]}),i.jsxs(Mh,{open:j,onOpenChange:y,children:[i.jsx(Ah,{asChild:!0,children:i.jsxs(X,{variant:"destructive",disabled:!m.trim(),className:"flex items-center space-x-2",children:[i.jsx(er,{className:"w-4 h-4"}),i.jsx("span",{children:"删除SQL"})]})}),i.jsxs(Su,{children:[i.jsxs(ju,{children:[i.jsx(Cu,{children:"确认删除"}),i.jsxs(Nu,{children:['您确定要删除SQL "',m,'" 吗？此操作不可撤销。']})]}),i.jsxs(Eu,{children:[i.jsx(X,{variant:"outline",onClick:()=>y(!1),children:"取消"}),i.jsx(X,{variant:"destructive",onClick:F,children:"确认删除"})]})]})]}),i.jsxs(ht,{variant:"destructive",children:[i.jsx(jo,{className:"h-4 w-4"}),i.jsxs(vt,{children:[i.jsx("strong",{children:"注意："}),"删除操作将永久移除SQL数据，请谨慎操作。"]})]})]})]})})]})]})}function fS(){return i.jsx(bp,{children:i.jsx(Lx,{children:i.jsxs("div",{className:"min-h-screen bg-slate-50",children:[i.jsx(kx,{children:i.jsxs(xn,{path:"/",element:i.jsx(Cw,{}),children:[i.jsx(xn,{index:!0,element:i.jsx(Mw,{})}),i.jsx(xn,{path:"usecase",element:i.jsx(md,{})}),i.jsx(xn,{path:"usecase/add",element:i.jsx(md,{})}),i.jsx(xn,{path:"sql",element:i.jsx(hd,{})}),i.jsx(xn,{path:"sql/add",element:i.jsx(hd,{})})]})}),i.jsx(yw,{})]})})})}Rp(document.getElementById("root")).render(i.jsx(d.StrictMode,{children:i.jsx(bp,{children:i.jsx(fS,{})})}));
