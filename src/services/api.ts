import { ApiConfig, ApiResponse, KnowledgeBase, UseCaseItem, SqlItem, SearchParams, FileUploadResponse, DocumentListResponse, DocumentSegmentsResponse, DocumentSegmentSearchParams } from '../types';


// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

const DEFAULT_CONFIG: ApiConfig = {
  apiKey: import.meta.env.VITE_API_KEY || '',
  apiIp: import.meta.env.VITE_API_IP || 'localhost:8000',
};

// 获取知识库ID（从Python代码转换）
export async function getKnowledgeBaseId(ragName: string, config: ApiConfig = DEFAULT_CONFIG): Promise<string | null> {
  const headers = {
    'Authorization': `Bearer ${config.apiKey}`,
    'Content-Type': 'application/json',
  };

  const url = `http://${config.apiIp}/v1/datasets`;
  const params = new URLSearchParams({
    page: '1',
    limit: '20'
  });

  try {
    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers,
    });

    if (response.ok) {
      const result = await response.json();
      const data = result.data;
      
      for (const item of data) {
        if (item.name === ragName) {
          console.log(`RAG ID for ${ragName} is ${item.id}`);
          return item.id;
        }
      }
    } else {
      console.error(`Request failed with status code ${response.status}`);
      console.error(await response.text());
    }
  } catch (error) {
    console.error('Error fetching knowledge base ID:', error);
  }

  return null;
}

// 获取知识库列表
export async function getKnowledgeBases(config: ApiConfig = DEFAULT_CONFIG): Promise<ApiResponse<KnowledgeBase[]>> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // 添加API密钥到请求头
  if (config.apiKey) {
    headers['X-API-Key'] = config.apiKey;
  }
  if (config.apiIp) {
    headers['X-API-IP'] = config.apiIp;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/knowledge-base/list`, {
      method: 'GET',
      headers,
    });

    if (response.ok) {
      const result = await response.json();
      return result;
    } else {
      const errorData = await response.json().catch(() => ({ error: '未知错误' }));
      return {
        success: false,
        error: errorData.error || `请求失败，状态码: ${response.status}`,
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `网络错误: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
}

// 搜索用例经验数据
export async function searchUseCases(
  params: SearchParams,
  config: ApiConfig = DEFAULT_CONFIG
): Promise<ApiResponse<UseCaseItem[]>> {
  try {
    const searchParams = new URLSearchParams();
    if (params.query) searchParams.append('query', params.query);
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.knowledge_base_id) searchParams.append('knowledge_base_id', params.knowledge_base_id);

    const response = await fetch(`${API_BASE_URL}/usecase/search?${searchParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const result = await response.json();
      return result;
    } else {
      const errorData = await response.json().catch(() => ({ error: '搜索失败' }));
      return {
        success: false,
        error: errorData.error || '搜索失败',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `搜索失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
}

// 搜索SQL经验数据
export async function searchSqlItems(
  params: SearchParams,
  config: ApiConfig = DEFAULT_CONFIG
): Promise<ApiResponse<SqlItem[]>> {
  try {
    const searchParams = new URLSearchParams();
    if (params.query) searchParams.append('query', params.query);
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.knowledge_base_id) searchParams.append('knowledge_base_id', params.knowledge_base_id);

    const response = await fetch(`${API_BASE_URL}/sql/search?${searchParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const result = await response.json();
      return result;
    } else {
      const errorData = await response.json().catch(() => ({ error: '搜索失败' }));
      return {
        success: false,
        error: errorData.error || '搜索失败',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `搜索失败: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
}

// 上传用例经验数据（从Python代码转换）
export async function uploadUseCaseData(
  csvFile: File,
  ragId: string,
  config: ApiConfig = DEFAULT_CONFIG
): Promise<FileUploadResponse> {
  try {
    const formData = new FormData();
    formData.append('file', csvFile);
    formData.append('rag_id', ragId);

    const headers: Record<string, string> = {};
    if (config.apiKey) {
      headers['X-API-Key'] = config.apiKey;
    }
    if (config.apiIp) {
      headers['X-API-IP'] = config.apiIp;
    }

    const response = await fetch(`${API_BASE_URL}/usecase/upload`, {
      method: 'POST',
      headers,
      body: formData,
    });

    if (response.ok) {
      const result = await response.json();
      return result;
    } else {
      const errorData = await response.json().catch(() => ({ message: '上传失败' }));
      return {
        success: false,
        message: errorData.message || '上传失败',
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `上传错误: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
}

// 上传SQL经验数据（从Python代码转换）
export async function uploadSqlData(
  txtFile: File,
  ragId: string,
  config: ApiConfig = DEFAULT_CONFIG
): Promise<FileUploadResponse> {
  try {
    const formData = new FormData();
    formData.append('file', txtFile);
    formData.append('rag_id', ragId);

    const headers: Record<string, string> = {};
    if (config.apiKey) {
      headers['X-API-Key'] = config.apiKey;
    }
    if (config.apiIp) {
      headers['X-API-IP'] = config.apiIp;
    }

    const response = await fetch(`${API_BASE_URL}/sql/upload`, {
      method: 'POST',
      headers,
      body: formData,
    });

    if (response.ok) {
      const result = await response.json();
      return result;
    } else {
      const errorData = await response.json().catch(() => ({ message: '上传失败' }));
      return {
        success: false,
        message: errorData.message || '上传失败',
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `上传错误: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
}

// 获取知识库文档列表
export async function getKnowledgeBaseDocuments(
  knowledgeBaseId: string,
  params: { page?: number; limit?: number } = {},
  config: ApiConfig = DEFAULT_CONFIG
): Promise<ApiResponse<DocumentListResponse>> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
  
  try {
    console.log(`[API] 获取文档列表: ${knowledgeBaseId}`);
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (config.apiKey) {
      headers['X-API-Key'] = config.apiKey;
      console.debug('[API] API密钥:', config.apiKey.substring(0, 4) + '****');
    }
    if (config.apiIp) {
      headers['X-API-IP'] = config.apiIp;
      console.debug('[API] API IP:', config.apiIp);
    }
    
    // 构造查询字符串
    const query = new URLSearchParams();
    if (params.page) query.set('page', params.page.toString());
    if (params.limit) query.set('limit', params.limit.toString());
    
    const url = new URL(
      `${API_BASE_URL}/knowledge-base/${knowledgeBaseId}/documents`
    );
    url.search = query.toString();
    
    console.debug('[API] 请求URL:', url.toString());
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers,
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      // 尝试获取错误详情
      let errorBody: string | object = await response.text();
      
      try {
        errorBody = JSON.parse(errorBody as string);
      } catch {
        // 保持文本格式
      }
      
      throw new RequestError(
        `请求失败: ${response.status} ${response.statusText}`,
        response.status,
        errorBody
      );
    }
    
    try {
      const result: DocumentListResponse = await response.json();
      console.debug('[API] 获取文档成功:', result);
      return {
        success: true,
        data: result
      };
    } catch (parseError) {
      throw new ResponseError('无效的JSON响应', parseError);
    }
    
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error instanceof RequestError || error instanceof ResponseError) {
      return {
        success: false,
        error: error.message
      };
    }
    
    if (error.name === 'AbortError') {
      return {
        success: false,
        error: '请求超时 (15秒)'
      };
    }
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        success: false,
        error: '网络连接失败，请检查API服务器是否可用'
      };
    }
    
    return {
      success: false,
      error: `未知错误: ${error instanceof Error ? error.message : JSON.stringify(error)}`
    };
  }
}

// 获取文档分段列表
export async function getDocumentSegments(
  knowledgeBaseId: string,
  documentId: string,
  params: DocumentSegmentSearchParams = {},
  config: ApiConfig = DEFAULT_CONFIG
): Promise<ApiResponse<DocumentSegmentsResponse>> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

  try {
    console.log(`[API] 获取文档分段: 知识库ID=${knowledgeBaseId}, 文档ID=${documentId}, 参数:`, params);

    const headers: HeadersInit = {
      'Authorization': `Bearer ${config.apiKey}`,
      'Content-Type': 'application/json',
    };

    // 构造查询字符串
    const query = new URLSearchParams();
    if (params.page) query.set('page', params.page.toString());
    if (params.limit) query.set('limit', params.limit.toString());
    if (params.query) query.set('keyword', params.query); // 使用keyword参数进行后端过滤
    query.set('enabled', 'all'); // 获取所有状态的分段

    const url = `http://${config.apiIp}/v1/datasets/${knowledgeBaseId}/documents/${documentId}/segments`;
    const fullUrl = `${url}?${query.toString()}`;

    console.debug('[API] 请求URL:', fullUrl);

    const response = await fetch(fullUrl, {
      method: 'GET',
      headers,
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    
    if (!response.ok) {
      let errorBody: string | object = await response.text();
      
      try {
        errorBody = JSON.parse(errorBody as string);
      } catch {
        // errorBody 保持为字符串
      }
      
      console.error(`[API] 获取文档分段失败: ${response.status}`, errorBody);
      
      return {
        success: false,
        error: `HTTP ${response.status}: ${
          typeof errorBody === 'object' && errorBody && 'message' in errorBody
            ? (errorBody as any).message
            : (errorBody as string) || response.statusText
        }`
      };
    }
    
    const result = await response.json();
    console.log('[API] 获取文档分段成功:', {
      documentId,
      query: params.query,
      totalReturned: result.data?.length || 0,
      hasMore: result.has_more,
      total: result.total
    });
    
    return {
      success: true,
      data: result
    };
    
  } catch (error: any) {
    clearTimeout(timeoutId);
    console.error('[API] 获取文档分段异常:', error);
    
    if (error.name === 'AbortError') {
      return {
        success: false,
        error: '请求超时，请检查网络连接'
      };
    }
    
    return {
      success: false,
      error: error.message || '获取文档分段时发生未知错误'
    };
  }
}

// 搜索所有文档的分段（根据关键词）
export async function searchAllDocumentSegments(
  knowledgeBaseId: string,
  query: string,
  documentIds: string[],
  config: ApiConfig = DEFAULT_CONFIG
): Promise<ApiResponse<DocumentSegmentsResponse[]>> {
  try {
    console.log(`[API] 搜索所有文档分段: 知识库ID=${knowledgeBaseId}, 查询词="${query}", 文档数=${documentIds.length}`);

    // 并发查询所有文档的分段，直接传递query参数让后端过滤
    const promises = documentIds.map(documentId =>
      getDocumentSegments(knowledgeBaseId, documentId, {
        page: 1,
        limit: 100,
        query: query // 传递查询词，后端会作为keyword参数过滤
      }, config)
    );

    const results = await Promise.allSettled(promises);
    const successResults: DocumentSegmentsResponse[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success && result.value.data) {
        const segments = result.value.data.data;
        console.log(`[API] 文档 ${documentIds[index]} 返回 ${segments.length} 个匹配分段`);

        // 后端已经过滤过了，直接使用结果
        if (segments.length > 0) {
          console.log(`[API] 文档 ${documentIds[index]} 包含匹配分段:`,
            segments.map(segment => ({
              position: segment.position,
              contentPreview: segment.content.substring(0, 100) + '...'
            }))
          );
          successResults.push(result.value.data);
        }
      } else if (result.status === 'rejected') {
        console.warn(`[API] 文档 ${documentIds[index]} 查询失败:`, result.reason);
      } else if (result.status === 'fulfilled' && !result.value.success) {
        console.warn(`[API] 文档 ${documentIds[index]} API调用失败:`, result.value.error);
      }
    });

    const totalSegments = successResults.reduce((sum, doc) => sum + doc.data.length, 0);
    console.log(`[API] 搜索完成，找到 ${successResults.length} 个文档包含 ${totalSegments} 个匹配分段`);

    return {
      success: true,
      data: successResults
    };

  } catch (error: any) {
    console.error('[API] 搜索所有文档分段异常:', error);

    return {
      success: false,
      error: error.message || '搜索文档分段时发生未知错误'
    };
  }
}

// 自定义错误类
class RequestError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public details?: any
  ) {
    super(message);
    this.name = 'RequestError';
  }
}

class ResponseError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'ResponseError';
  }
}

// 工具函数：将JSON转换为CSV
export function jsonToCsv(jsonData: UseCaseItem[]): string {
  if (jsonData.length === 0) return '';

  // CSV头部
  const headers = ['id', 'name', 'description', 'type', 'db_type', 'content'];
  
  // 转换数据行
  const rows = jsonData.map(item => [
    item.id,
    item.name,
    item.description,
    item.type,
    item.db_type,
    // Array.isArray(item.content) ? item.content.join(';') : item.content
    // 将内容数组转换为字符串数组表示形式
    Array.isArray(item.content) 
      ? `[${item.content.map(str => `'${str.replace(/'/g, "''")}'`).join(',')}]`
      : `'${String(item.content).replace(/'/g, "''")}'`
  ]);

  // 组合CSV内容
  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n');

  return csvContent;
}

// 工具函数：创建并下载文件
export function downloadFile(content: string, filename: string, type: string): File {
  const blob = new Blob([content], { type });
  return new File([blob], filename, { type });
}

// 删除文档
export async function deleteDocument(
  knowledgeBaseId: string,
  documentId: string,
  config: ApiConfig = DEFAULT_CONFIG
): Promise<ApiResponse<{ message: string }>> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 15000);

  try {
    console.log(`[API] 删除文档: 知识库ID=${knowledgeBaseId}, 文档ID=${documentId}`);

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (config.apiKey) {
      headers['X-API-Key'] = config.apiKey;
    }
    if (config.apiIp) {
      headers['X-API-IP'] = config.apiIp;
    }

    const url = `${API_BASE_URL}/knowledge-base/${knowledgeBaseId}/documents/${documentId}`;

    console.debug('[API] 删除文档URL:', url);

    const response = await fetch(url, {
      method: 'DELETE',
      headers,
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      let errorBody: string | object = await response.text();

      try {
        errorBody = JSON.parse(errorBody as string);
      } catch {
        // 保持文本格式
      }

      throw new RequestError(
        `删除文档失败: ${response.status} ${response.statusText}`,
        response.status,
        errorBody
      );
    }

    try {
      const result = await response.json();
      console.debug('[API] 删除文档成功:', result);
      return {
        success: true,
        data: result
      };
    } catch (parseError) {
      throw new ResponseError('无效的JSON响应', parseError);
    }
  } catch (error) {
    clearTimeout(timeoutId);
    console.error('[API] 删除文档失败:', error);

    if (error instanceof RequestError || error instanceof ResponseError) {
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : '删除文档失败'
    };
  }
}

// 删除文档分段
export async function deleteDocumentSegment(
  knowledgeBaseId: string,
  documentId: string,
  segmentId: string,
  config: ApiConfig = DEFAULT_CONFIG
): Promise<ApiResponse<{ message: string }>> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 15000);

  try {
    console.log(`[API] 删除分段: 知识库ID=${knowledgeBaseId}, 文档ID=${documentId}, 分段ID=${segmentId}`);

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (config.apiKey) {
      headers['X-API-Key'] = config.apiKey;
    }
    if (config.apiIp) {
      headers['X-API-IP'] = config.apiIp;
    }

    const url = `${API_BASE_URL}/knowledge-base/${knowledgeBaseId}/documents/${documentId}/segments/${segmentId}`;

    console.debug('[API] 删除分段URL:', url);

    const response = await fetch(url, {
      method: 'DELETE',
      headers,
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      let errorBody: string | object = await response.text();

      try {
        errorBody = JSON.parse(errorBody as string);
      } catch {
        // 保持文本格式
      }

      throw new RequestError(
        `删除分段失败: ${response.status} ${response.statusText}`,
        response.status,
        errorBody
      );
    }

    try {
      const result = await response.json();
      console.debug('[API] 删除分段成功:', result);
      return {
        success: true,
        data: result
      };
    } catch (parseError) {
      throw new ResponseError('无效的JSON响应', parseError);
    }
  } catch (error) {
    clearTimeout(timeoutId);
    console.error('[API] 删除分段失败:', error);

    if (error instanceof RequestError || error instanceof ResponseError) {
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : '删除分段失败'
    };
  }
}
