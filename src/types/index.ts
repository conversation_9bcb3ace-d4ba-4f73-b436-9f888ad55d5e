// 知识库类型定义
export interface KnowledgeBase {
  id: string;
  name: string;
  description?: string;
  type: 'usecase' | 'sql';
  created_at?: string;
  updated_at?: string;
}

// 用例经验数据类型
export interface UseCaseItem {
  id: string;
  name: string;
  description: string;
  type: string;
  db_type: string;
  content: string[];
  document_id?: string; // 添加可选的文档ID字段
}

// SQL经验数据类型
export interface SqlItem {
  id: string;
  name: string;
  description: string;
  sql_content: string;
  tags?: string[];
  document_id?: string; // 添加可选的文档ID字段
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 搜索参数类型
export interface SearchParams {
  query?: string;
  page?: number;
  limit?: number;
  knowledge_base_id?: string;
}

// 文件上传响应类型
export interface FileUploadResponse {
  success: boolean;
  file_id?: string;
  message?: string;
}

// 外部API配置类型
export interface ApiConfig {
  apiKey: string;
  apiIp: string;
  ragId?: string;
}

// 组件props类型
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// 表格数据类型
export interface TableColumn {
  key: string;
  title: string;
  width?: number;
  render?: (value: any, record: any) => React.ReactNode;
}

// 删除操作类型
export interface DeleteOperation {
  type: 'usecase' | 'sql';
  name: string;
  knowledgeBaseId: string;
}

// 文档类型
export interface DocumentItem {
  id: string;
  name: string;
  type: string;
  status: string;
  created_at: string;
  updated_at: string;
  word_count: number;
  character_count: number;
  is_archived: boolean;
  document_type: string;
}

// 分页信息类型
export interface Pagination {
  page: number;
  limit: number;
  total: number;
}

// 文档列表响应类型
export interface DocumentListResponse {
  data: DocumentItem[];
  pagination: Pagination;
}

// 文档分段类型
export interface DocumentSegment {
  id: string;
  position: number;
  document_id: string;
  content: string;
  answer: string;
  word_count: number;
  tokens: number;
  keywords: string[];
  index_node_id: string;
  index_node_hash: string;
  hit_count: number;
  enabled: boolean;
  disabled_at: string | null;
  disabled_by: string | null;
  status: string;
  created_by: string;
  created_at: number;
  indexing_at: number;
  completed_at: number;
  error: string | null;
  stopped_at: string | null;
}

// 文档分段列表响应类型
export interface DocumentSegmentsResponse {
  data: DocumentSegment[];
  doc_form: string;
  has_more: boolean;
  limit: number;
  total: number;
  page: number;
}

// 文档分段搜索参数类型
export interface DocumentSegmentSearchParams {
  query?: string;
  page?: number;
  limit?: number;
}

