import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '../lib/utils';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { Separator } from './ui/separator';
import { Database, FileText, Search, Plus } from 'lucide-react';
import { getKnowledgeBases } from '../services/api';
import { KnowledgeBase } from '../types';

export default function Layout() {
  const location = useLocation();
  const navigate = useNavigate();
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [usecaseKB, setUsecaseKB] = useState<KnowledgeBase | null>(null);
  const [sqlKB, setSqlKB] = useState<KnowledgeBase | null>(null);

  useEffect(() => {
    loadKnowledgeBases();
  }, []);

  const loadKnowledgeBases = async () => {
    try {
      const response = await getKnowledgeBases();
      if (response.success && response.data) {
        setKnowledgeBases(response.data);

        // 找到用例经验库和SQL经验库
        const usecaseBase = response.data.find(kb => kb.type === 'usecase');
        const sqlBase = response.data.find(kb => kb.type === 'sql');

        setUsecaseKB(usecaseBase || null);
        setSqlKB(sqlBase || null);
      }
    } catch (error) {
      console.error('加载知识库失败:', error);
    }
  };

  const handleNavigateToKB = (type: 'usecase' | 'sql') => {
    const kb = type === 'usecase' ? usecaseKB : sqlKB;
    if (kb) {
      navigate(`/${type}`, { state: { knowledgeBase: kb } });
    } else {
      // 如果没有找到对应的知识库，跳转到知识库选择页面
      navigate('/');
    }
  };

  const handleNavigateToAdd = (type: 'usecase' | 'sql') => {
    const kb = type === 'usecase' ? usecaseKB : sqlKB;
    if (kb) {
      navigate(`/${type}`, {
        state: {
          knowledgeBase: kb,
          defaultTab: type === 'usecase' ? 'add' : 'add-sql'
        }
      });
    } else {
      // 如果没有找到对应的知识库，跳转到知识库选择页面
      navigate('/');
    }
  };

  const navigation = [
    {
      name: '知识库选择',
      href: '/',
      icon: Database,
      current: location.pathname === '/',
      onClick: () => navigate('/'),
    },
    {
      name: '用例经验管理',
      href: '/usecase',
      icon: FileText,
      current: location.pathname.startsWith('/usecase'),
      onClick: () => handleNavigateToKB('usecase'),
    },
    {
      name: 'SQL经验管理',
      href: '/sql',
      icon: Search,
      current: location.pathname.startsWith('/sql'),
      onClick: () => handleNavigateToKB('sql'),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* 顶部导航栏 */}
      <header className="bg-white border-b border-slate-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                  <Database className="w-5 h-5 text-white" />
                </div>
                <h1 className="text-xl font-bold text-slate-900">
                  数据库知识库管理平台
                </h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-sm text-slate-600">
                当前时间: {new Date().toLocaleString('zh-CN')}
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 侧边导航 */}
          <aside className="lg:col-span-1">
            <Card className="p-6">
              <nav className="space-y-2">
                {navigation.map((item) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={item.name}
                      onClick={item.onClick}
                      className={cn(
                        'w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors text-left',
                        item.current
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                      )}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.name}</span>
                    </button>
                  );
                })}
              </nav>

              <Separator className="my-6" />

              <div className="space-y-4">
                <h3 className="text-sm font-medium text-slate-900">快速操作</h3>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => handleNavigateToAdd('usecase')}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    添加用例
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => handleNavigateToAdd('sql')}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    添加SQL
                  </Button>
                </div>
              </div>
            </Card>
          </aside>

          {/* 主内容区域 */}
          <main className="lg:col-span-3">
            <Outlet />
          </main>
        </div>
      </div>

      {/* 页脚 */}
      <footer className="bg-white border-t border-slate-200 mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="text-center text-sm text-slate-500">
            © 2025 数据库知识库管理平台. 版权所有.
          </div>
        </div>
      </footer>
    </div>
  );
}
