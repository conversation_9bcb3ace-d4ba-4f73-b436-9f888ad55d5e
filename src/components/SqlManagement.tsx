import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { useToast } from '../hooks/use-toast';
import { searchSqlItems, uploadSqlData, downloadFile, getKnowledgeBaseDocuments, getDocumentSegments, searchAllDocumentSegments } from '../services/api';
import { SqlItem, KnowledgeBase, DocumentItem, Pagination, DocumentSegment } from '../types';
import { Code, Search, Plus, Trash2, Upload, AlertCircle, CheckCircle2, Copy, FileText, RefreshCw } from 'lucide-react';

export default function SqlManagement() {
  const location = useLocation();
  const knowledgeBase = location.state?.knowledgeBase as KnowledgeBase;
  
  const [sqlItems, setSqlItems] = useState<SqlItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [textInput, setTextInput] = useState('');
  const [deleteTarget, setDeleteTarget] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const { toast } = useToast();
  const [fileName, setFileName] = useState('');
  const [documents, setDocuments] = useState<DocumentItem[]>([]);
  const [pagination, setPagination] = useState<Pagination>({ page: 1, limit: 20, total: 0 });
  const [loadingDocuments, setLoadingDocuments] = useState(false);

  // 新增：文档分段相关状态
  const [documentSegments, setDocumentSegments] = useState<DocumentSegment[]>([]);
  const [loadingSegments, setLoadingSegments] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>('');
  const [searchMode, setSearchMode] = useState<'keyword' | 'document'>('keyword');

  useEffect(() => {
    if (knowledgeBase) {
      fetchDocuments(); // 加载文档列表
      // 不在初始化时调用搜索，因为searchTerm为空
    }
  }, [knowledgeBase]);

  // 获取知识库文档列表
  const fetchDocuments = async (page = 1, limit = 20) => {
    setLoadingDocuments(true);
    try {
      // const response = await getKnowledgeBaseDocuments(knowledgeBase?.id, { page, limit });
      const response = await getKnowledgeBaseDocuments(knowledgeBase.id, { page, limit }); 
      
      if (response.success && response.data) {
        setDocuments(response.data.data);
        setPagination(response.data.pagination);
      } else {
        toast({
          title: "获取文档列表失败",
          description: response.error || "无法加载文档列表",
          variant: "destructive",
        });
      }
    } catch (error) {
      const errorMessage = "网络连接异常,请稍后重试";
      toast({
        title: "获取文档列表异常",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoadingDocuments(false);
    }
  };

  // 判断输入是否为文档ID（UUID格式）
  const isDocumentId = (input: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(input.trim());
  };



  // 获取文档分段
  const fetchDocumentSegments = async (documentId: string, query?: string) => {
    setLoadingSegments(true);
    try {
      const response = await getDocumentSegments(knowledgeBase.id, documentId, {
        query: query,
        page: 1,
        limit: 100
      });

      if (response.success && response.data) {
        setDocumentSegments(response.data.data);

        // 解析分段内容为SQL数据
        const parsedSqlItems = parseSegmentToSqlItems(response.data.data);
        setSqlItems(parsedSqlItems);

        setSelectedDocumentId(documentId);

        toast({
          title: "获取文档内容成功",
          description: `找到 ${parsedSqlItems.length} 条SQL记录`,
        });
      } else {
        toast({
          title: "获取文档内容失败",
          description: response.error || "无法获取文档分段",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "获取文档内容错误",
        description: "网络连接错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoadingSegments(false);
    }
  };

  // 搜索所有文档
  const searchAllDocuments = async (query: string): Promise<SqlItem[]> => {
    try {
      const documentIds = documents.map(doc => doc.id);
      console.log(`[搜索] 开始搜索文档分段，查询词: "${query}"，文档数: ${documentIds.length}`);

      if (documentIds.length === 0) {
        console.warn('[搜索] 当前知识库中没有文档');
        return [];
      }

      console.log('[搜索] 文档ID列表:', documentIds);
      const response = await searchAllDocumentSegments(knowledgeBase.id, query, documentIds);
      console.log('[搜索] API响应:', response);

      if (response.success && response.data) {
        console.log(`[搜索] API返回 ${response.data.length} 个文档的搜索结果`);

        // 合并所有文档的分段结果
        const allSegments: DocumentSegment[] = [];
        response.data.forEach((docResponse, index) => {
          console.log(`[搜索] 文档 ${index + 1} 包含 ${docResponse.data.length} 个匹配分段`);
          allSegments.push(...docResponse.data);
        });

        console.log(`[搜索] 总共找到 ${allSegments.length} 个匹配分段`);

        // 解析分段内容为SQL数据
        const parsedSqlItems = parseSegmentToSqlItems(allSegments);

        // 同时保留原始分段数据供调试
        setDocumentSegments(allSegments);
        setSelectedDocumentId('');

        console.log(`[搜索] 在 ${response.data.length} 个文档中找到 ${parsedSqlItems.length} 条SQL记录`);
        return parsedSqlItems;
      } else {
        console.warn('[搜索] 搜索失败:', response.error);
        return [];
      }
    } catch (error) {
      console.error('[搜索] 搜索错误:', error);
      return [];
    }
  };

  // 解析文档分段内容为SQL数据
  const parseSegmentToSqlItems = (segments: DocumentSegment[]): SqlItem[] => {
    console.log(`[解析] 开始解析 ${segments.length} 个文档分段`);
    const parsedSqlItems: SqlItem[] = [];

    segments.forEach((segment, segmentIndex) => {
      try {
        const content = segment.content.trim();
        console.log(`[解析] 分段 ${segmentIndex + 1}/${segments.length}:`, {
          id: segment.id,
          contentPreview: content.substring(0, 100) + '...'
        });

        // 按行分割内容
        const lines = content.split('\n').map(line => line.trim()).filter(line => line);

        if (lines.length === 0) {
          console.log(`[解析] 分段 ${segmentIndex + 1} 内容为空，跳过`);
          return;
        }

        // 解析第一行：数据库类型:描述
        const firstLine = lines[0];
        let dbType = 'nan';
        let description = firstLine;

        if (firstLine.includes(':')) {
          const colonIndex = firstLine.indexOf(':');
          const beforeColon = firstLine.substring(0, colonIndex).trim();
          const afterColon = firstLine.substring(colonIndex + 1).trim();

          if (beforeColon) {
            dbType = beforeColon;
            description = afterColon || firstLine;
          }
        }

        // 查找SQL内容
        let sqlContent = '';
        let foundSql = false;

        for (let i = 1; i < lines.length; i++) {
          const line = lines[i];

          // 跳过 :ident: 行
          if (line === ':ident:') {
            continue;
          }

          // 检查是否是SQL代码块
          if (line.startsWith('```sql') || line.startsWith('```')) {
            // 找到SQL代码块开始
            foundSql = true;
            continue;
          }

          if (line === '```' && foundSql) {
            // SQL代码块结束
            break;
          }

          if (foundSql) {
            // 收集SQL内容
            sqlContent += (sqlContent ? '\n' : '') + line;
          } else {
            // 如果没有代码块标记，检查是否包含SQL关键词
            const sqlKeywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP', 'WITH'];
            const hasSQL = sqlKeywords.some(keyword =>
              line.toUpperCase().includes(keyword.toUpperCase())
            );

            if (hasSQL) {
              sqlContent += (sqlContent ? '\n' : '') + line;
            }
          }
        }

        // 如果没有找到SQL内容，尝试从整个内容中提取
        if (!sqlContent) {
          const allText = lines.slice(1).join('\n');
          const sqlKeywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP', 'WITH'];
          const hasSQL = sqlKeywords.some(keyword =>
            allText.toUpperCase().includes(keyword.toUpperCase())
          );

          if (hasSQL) {
            // 移除代码块标记
            sqlContent = allText
              .replace(/```sql/g, '')
              .replace(/```/g, '')
              .replace(/:ident:/g, '')
              .trim();
          }
        }

        // 只有当找到SQL内容时才创建记录
        if (sqlContent) {
          const sqlItem: SqlItem = {
            id: `${segment.document_id}-${segmentIndex}`,
            name: dbType, // 数据库类型作为名称
            description: description,
            sql_content: sqlContent.trim(),
            tags: [dbType !== 'nan' ? dbType : '未知数据库'],
            document_id: segment.document_id
          };

          console.log(`[解析] 成功解析分段 ${segmentIndex + 1}:`, {
            dbType,
            description,
            sqlContentPreview: sqlContent.substring(0, 50) + '...'
          });

          parsedSqlItems.push(sqlItem);
        } else {
          console.log(`[解析] 分段 ${segmentIndex + 1} 未找到SQL内容，跳过`);
        }

      } catch (error) {
        console.warn('解析文档分段失败:', segment.id, error);
      }
    });

    console.log(`[解析] 解析完成，共生成 ${parsedSqlItems.length} 条SQL记录`);
    return parsedSqlItems;
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      toast({
        title: "搜索提示",
        description: "请输入搜索关键词或文档ID",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    setSqlItems([]); // 清空SQL搜索结果

    try {
      // 判断输入的是文档ID还是关键词
      if (isDocumentId(searchTerm)) {
        setSearchMode('document');
        await fetchDocumentSegments(searchTerm.trim());
      } else {
        setSearchMode('keyword');

        // 存储所有搜索结果
        let allSqlItems: SqlItem[] = [];

        // 先搜索传统SQL
        const sqlResponse = await searchSqlItems({
          query: searchTerm,
          knowledge_base_id: knowledgeBase?.id
        });

        if (sqlResponse.success && sqlResponse.data) {
          allSqlItems = [...sqlResponse.data];
        }

        // 然后搜索文档分段
        const documentSqlItems = await searchAllDocuments(searchTerm);
        if (documentSqlItems && documentSqlItems.length > 0) {
          allSqlItems = [...allSqlItems, ...documentSqlItems];
        }

        // 设置合并后的结果
        setSqlItems(allSqlItems);

        // 显示搜索结果提示
        if (allSqlItems.length > 0) {
          toast({
            title: "搜索完成",
            description: `找到 ${allSqlItems.length} 条相关SQL记录`,
          });
        } else {
          toast({
            title: "搜索完成",
            description: `未找到包含 "${searchTerm}" 的相关SQL`,
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      toast({
        title: "搜索错误",
        description: "网络连接错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddSql = async () => {
    if (!textInput.trim()) {
      toast({
        title: "添加失败",
        description: "请输入SQL内容",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUploading(true);

      // 创建TXT文件
      const finalFileName = fileName.trim()
        ? `${encodeURIComponent(fileName.trim())}.txt`
        : `usecases_${new Date().toISOString().slice(0,19).replace(/[-T:]/g, '')}.txt`; 
      const txtFile = downloadFile(textInput, finalFileName, 'text/plain');

      // 上传文件
      if (knowledgeBase) {
        const response = await uploadSqlData(txtFile, knowledgeBase.id);
        
        if (response.success) {
          toast({
            title: "上传成功",
            description: response.message || "SQL数据已成功添加到知识库",
          });
          setTextInput('');
          handleSearch(); // 刷新列表
          fetchDocuments(); // 刷新文档列表
        } else {
          toast({
            title: "上传失败",
            description: response.message || "上传过程中发生错误",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      toast({
        title: "添加失败",
        description: error instanceof Error ? error.message : "上传过程中发生错误",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteSql = async () => {
    if (!deleteTarget.trim()) {
      toast({
        title: "删除失败",
        description: "请输入要删除的SQL名称",
        variant: "destructive",
      });
      return;
    }

    try {
      // 这里应该调用删除API
      // 暂时模拟删除操作
      const filteredSqlItems = sqlItems.filter(item => item.name !== deleteTarget);
      setSqlItems(filteredSqlItems);
      
      toast({
        title: "删除成功",
        description: `SQL "${deleteTarget}" 已被删除`,
      });
      
      setDeleteTarget('');
      setShowDeleteDialog(false);
      fetchDocuments(); // 刷新文档列表
    } catch (error) {
      toast({
        title: "删除失败",
        description: "删除过程中发生错误",
        variant: "destructive",
      });
    }
  };

  const copySqlToClipboard = async (sql: string) => {
    try {
      await navigator.clipboard.writeText(sql);
      toast({
        title: "复制成功",
        description: "SQL语句已复制到剪贴板",
      });
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      });
    }
  };

  // 格式化日期
  const formatDate = (timestamp: string | number) => {
    // 确保时间戳是数字类型
    const ts = typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;

    // 判断时间戳的单位（秒或毫秒）
    const isSeconds = ts < 9999999999; // 小于 10^10 的认为是秒级时间戳

    // 转换为Date对象
    const date = new Date(isSeconds ? ts * 1000 : ts);

    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 文档状态映射
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <Badge variant="secondary">可用</Badge>;
      case 'pending':
        return <Badge variant="secondary">处理中</Badge>;
      case 'error':
        return <Badge variant="destructive">错误</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handlePageChange = (newPage: number) => {
    fetchDocuments(newPage, pagination.limit);
  };

  const handleLimitChange = (newLimit: number) => {
    fetchDocuments(1, newLimit);
  };

  const sampleTextData = `fqs查询,agg函数在having中
:ident:
\`\`\`sql
select * from fqs_shard_1 group by id having count(*) > 1000;
select max(max) from (select max(dt) from fqs_multi_shard_1 group by id,deptno,ts having avg(age) = 10);
select max(avg) from (select avg(age) from fqs_list_1 group by address,dt having max(dt) > '2025-10-01');
\`\`\`

TDSQL-PG:复杂聚合查询,窗口函数与分组
:ident:
\`\`\`sql
select 
  id,
  name,
  row_number() over (partition by dept_id order by salary desc) as rank
from employees 
where status = 'active';

select 
  dept_id,
  avg(salary) as avg_salary,
  count(*) as emp_count
from employees 
group by dept_id 
having count(*) > 5;
\`\`\``;

  if (!knowledgeBase) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          请先选择一个知识库。<a href="/" className="underline">返回知识库选择</a>
        </AlertDescription>
      </Alert>
    );
  }



  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Code className="w-6 h-6" />
            <span>SQL经验知识库管理</span>
          </CardTitle>
          <CardDescription>
            当前知识库: {knowledgeBase.name} (ID: {knowledgeBase.id})
          </CardDescription>
          {/* 知识库文档列表 - 直接放在知识库信息下面 */}
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>SQL文档列表</span>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => fetchDocuments()}
                disabled={loadingDocuments}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loadingDocuments ? 'animate-spin' : ''}`} />
                刷新列表
              </Button>
            </CardTitle>
            <CardDescription>
              当前知识库中的所有文档，共 {documents.length} 个文档
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loadingDocuments ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : (
              <>
                {documents.length > 0 ? (
                  <>
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>文档ID</TableHead>
                            <TableHead>文档名称</TableHead>
                            <TableHead>类型</TableHead>
                            <TableHead>状态</TableHead>
                            <TableHead>创建时间</TableHead>
                            <TableHead>字数</TableHead>
                            <TableHead>操作</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {documents.map((doc) => (
                            <TableRow key={doc.id}>
                              <TableCell className="font-medium">
                                {doc.id}
                              </TableCell>
                              <TableCell className="font-medium">
                                {doc.name || '未命名文档'}
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">{doc.type}</Badge>
                              </TableCell>
                              <TableCell>
                                {getStatusBadge(doc.status)}
                              </TableCell>
                              <TableCell className="text-sm">
                                {formatDate(doc.created_at)}
                              </TableCell>
                              <TableCell className="text-right">
                                {doc.word_count?.toLocaleString() || 0}
                              </TableCell>
                              <TableCell>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => {
                                    navigator.clipboard.writeText(doc.id);
                                    toast({
                                      title: "已复制",
                                      description: "文档ID已复制到剪贴板",
                                    });
                                  }}
                                >
                                  <Copy className="w-3 h-3 mr-1" />
                                  复制ID
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                    {/* 分页组件 */}
                    <div className="flex items-center justify-between mt-4">
                      <div className="text-sm text-gray-500">
                        显示 {documents.length} 条中的第 {pagination.page} 页，共 {Math.ceil(pagination.total / pagination.limit)} 页
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page - 1)}
                          disabled={pagination.page <= 1}
                        >
                          上一页
                        </Button>
                        <span className="text-sm">
                          {pagination.page}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page + 1)}
                          disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
                        >
                          下一页
                        </Button>
                        <select 
                          value={pagination.limit}
                          onChange={(e) => handleLimitChange(Number(e.target.value))}
                          className="border rounded p-1 text-sm"
                        >
                          <option value={10}>10条/页</option>
                          <option value={20}>20条/页</option>
                          <option value={50}>50条/页</option>
                          <option value={100}>100条/页</option>
                        </select>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 space-y-4 text-center">
                    <FileText className="w-16 h-16 text-gray-300" />
                    <h3 className="text-lg font-medium">暂无文档</h3>
                    <p className="text-sm text-gray-500">
                      当前知识库中还没有任何文档，请上传SQL数据。
                    </p>
                    <Button 
                      variant="outline"
                      onClick={() => document.querySelector('button[data-state="active"]')?.dispatchEvent(new MouseEvent('click'))}
                    >
                      前往上传
                    </Button>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </CardHeader>
      </Card>

      {/* 功能选项卡 */}
      <Tabs defaultValue="search" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="search" className="flex items-center space-x-2">
            <Search className="w-4 h-4" />
            <span>查询SQL</span>
          </TabsTrigger>
          <TabsTrigger value="add" className="flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>新增SQL</span>
          </TabsTrigger>
          <TabsTrigger value="delete" className="flex items-center space-x-2">
            <Trash2 className="w-4 h-4" />
            <span>删除SQL</span>
          </TabsTrigger>
        </TabsList>



        {/* 查询SQL */}
        <TabsContent value="search">
          <Card>
            <CardHeader>
              <CardTitle>搜索SQL与文档内容</CardTitle>
              <CardDescription>
                输入关键词搜索SQL经验数据和文档内容，或输入文档ID查看具体文档分段
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-end space-x-4">
                <div className="flex-1">
                  <Label htmlFor="search-input">搜索关键词或文档ID</Label>
                  <Input
                    id="search-input"
                    placeholder="输入关键词搜索所有文档，或输入文档ID查看具体内容..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  />
                  <p className="text-xs text-slate-500 mt-1">
                    提示：输入 UUID 格式的文档ID可查看该文档的所有分段内容
                  </p>
                </div>
                <Button onClick={handleSearch} disabled={loading || loadingSegments}>
                  {loading || loadingSegments ? '搜索中...' : '搜索'}
                </Button>
              </div>

              {/* 搜索结果表格 */}
              {sqlItems.length > 0 && (
                <div className="space-y-4">
                  {/* 搜索模式提示 */}
                  <div className="flex items-center justify-between text-sm text-slate-600">
                    <span>
                      {searchMode === 'document'
                        ? `文档ID搜索结果 (文档: ${selectedDocumentId})`
                        : `关键词搜索结果 (关键词: "${searchTerm}")`
                      }
                    </span>
                    <span>共找到 {sqlItems.length} 条SQL记录</span>
                  </div>

                  <div className="border rounded-lg">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>文档ID</TableHead>
                          <TableHead>数据库类型</TableHead>
                          <TableHead>描述</TableHead>
                          <TableHead>SQL内容</TableHead>
                          <TableHead>标签</TableHead>
                          <TableHead>操作</TableHead>
                        </TableRow>
                      </TableHeader>
                    <TableBody>
                      {sqlItems.map((sqlItem) => (
                        <TableRow key={sqlItem.id}>
                          <TableCell className="font-mono text-sm">
                            {sqlItem.document_id || '未知'}
                          </TableCell>
                          <TableCell>
                            <Badge variant={sqlItem.name === 'nan' ? 'secondary' : 'default'}>
                              {sqlItem.name}
                            </Badge>
                          </TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate" title={sqlItem.description}>
                              {sqlItem.description}
                            </div>
                          </TableCell>
                          <TableCell className="max-w-md">
                            <div className="relative">
                              <pre className="bg-slate-50 p-3 rounded text-xs font-mono overflow-x-auto max-h-32 border">
                                <code>{sqlItem.sql_content}</code>
                              </pre>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {sqlItem.tags?.map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => copySqlToClipboard(sqlItem.sql_content)}
                              className="flex items-center space-x-1"
                            >
                              <Copy className="w-3 h-3" />
                              <span>复制</span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  </div>
                </div>
              )}

              {/* 空状态提示 */}
              {sqlItems.length === 0 && !loading && !loadingSegments && searchTerm && (
                <div className="text-center py-8 text-slate-500">
                  <Code className="w-12 h-12 mx-auto mb-4 text-slate-300" />
                  <p>暂无搜索结果，请尝试其他关键词或文档ID</p>
                  <p className="text-xs mt-2">
                    提示：系统会自动解析文档分段中的数据库类型、描述和SQL语句
                  </p>
                  <p className="text-xs mt-1">
                    支持格式：数据库类型:描述 + SQL代码块
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 新增SQL */}
        <TabsContent value="add">
          <Card>
            <CardHeader>
              <CardTitle>新增SQL数据</CardTitle>
              <CardDescription>
                输入SQL内容，系统将自动转换为TXT格式上传
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">

              <div>
                <Label htmlFor="file-name">经验SQL文件名（可选）</Label>
                <Input
                  id="file-name"
                  placeholder="输入自定义文件名（不包括扩展名）"
                  value={fileName}
                  onChange={(e) => setFileName(e.target.value)}
                />
                <p className="text-red-500 text-xs mt-1">
                    建议格式：<span className="font-medium">产品名_描述性名称</span>
                </p>
                <p className="text-xs text-slate-500 mt-1">
                  {fileName.trim()
                    ? `文件将保存为: ${fileName.trim()}.txt`
                    : '不填写将自动生成文件名 (如: usecases_202506241230.txt)'}
                </p>
              </div>

              <div>
                <Label htmlFor="text-input">SQL内容</Label>
                <Textarea
                  id="text-input"
                  placeholder={sampleTextData}
                  value={textInput}
                  onChange={(e) => setTextInput(e.target.value)}
                  rows={20}
                  className="font-mono text-sm"
                />
                <div className="text-sm text-slate-500 mt-2">
                  支持格式：[产品:]名称,描述 + :ident: + SQL代码块
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <Button
                  onClick={handleAddSql}
                  disabled={!textInput.trim() || isUploading}
                  className="flex items-center space-x-2"
                >
                  <Upload className="w-4 h-4" />
                  <span>{isUploading ? '上传中...' : '添加SQL'}</span>
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => setTextInput(sampleTextData)}
                >
                  使用示例数据
                </Button>
              </div>

              {/* 格式说明 */}
              <Alert>
                <CheckCircle2 className="h-4 w-4" />
                <AlertDescription>
                  <strong>文本格式要求：</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                    <li>每个SQL条目以"[产品:]名称,描述"开始,如果这个SQL是产品独有的,就需要加上产品</li>
                    <li>使用":ident:"作为分隔符</li>
                    <li>SQL代码可以用```sql代码块包装</li>
                    <li>支持多个SQL条目，每个条目独立分隔</li>
                    <li>系统将自动转换为TXT格式上传到知识库</li>
                  </ul>
                </AlertDescription>
              </Alert>

              {/* 示例展示 */}
              <Card className="bg-slate-50">
                <CardHeader>
                  <CardTitle className="text-sm">格式示例</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs font-mono overflow-x-auto">
{`fqs查询,agg函数在having中
:ident:
\`\`\`sql
select * from fqs_shard_1 group by id having count(*) > 1000;
\`\`\`

TDSQL-PG:复杂查询,嵌套查询示例
:ident:
\`\`\`sql
select max(max) from (select max(dt) from table group by id);
\`\`\``}
                  </pre>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 删除SQL */}
        <TabsContent value="delete">
          <Card>
            <CardHeader>
              <CardTitle>删除SQL</CardTitle>
              <CardDescription>
                输入要删除的SQL名称
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="delete-input">SQL名称</Label>
                <Input
                  id="delete-input"
                  placeholder="输入要删除的SQL名称..."
                  value={deleteTarget}
                  onChange={(e) => setDeleteTarget(e.target.value)}
                />
              </div>

              <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <DialogTrigger asChild>
                  <Button 
                    variant="destructive" 
                    disabled={!deleteTarget.trim()}
                    className="flex items-center space-x-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>删除SQL</span>
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>确认删除</DialogTitle>
                    <DialogDescription>
                      您确定要删除SQL "{deleteTarget}" 吗？此操作不可撤销。
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
                      取消
                    </Button>
                    <Button variant="destructive" onClick={handleDeleteSql}>
                      确认删除
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>注意：</strong>删除操作将永久移除SQL数据，请谨慎操作。
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

