{"hash": "277543be", "configHash": "8e00b4f3", "lockfileHash": "e5e6a24c", "browserHash": "e2c836af", "optimized": {"react": {"src": "../../.pnpm/react@18.3.1/node_modules/react/index.js", "file": "react.js", "fileHash": "c053ae4f", "needsInterop": true}, "react-dom": {"src": "../../.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "2e60d130", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../.pnpm/react@18.3.1/node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "645ab6a9", "needsInterop": true}, "react/jsx-runtime": {"src": "../../.pnpm/react@18.3.1/node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "78ddfc08", "needsInterop": true}, "@radix-ui/react-dialog": {"src": "../../.pnpm/@radix-ui+react-dialog@1.1.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+reac_979338a14129bfbd4b93c15b369f3450/node_modules/@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "06448a60", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../.pnpm/@radix-ui+react-label@2.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_69050205ca3d2aecc0d76a03e1b44443/node_modules/@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "34f6bba3", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../.pnpm/@radix-ui+react-separator@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_a935beed276764a445ba167cf5230d34/node_modules/@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "31d37207", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "150c743c", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../.pnpm/@radix-ui+react-tabs@1.1.12_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_039f4c9042ddc46bc6b9dbdec180c93a/node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "358553a4", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../.pnpm/@radix-ui+react-toast@1.2.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_f2d63fe9a772cc94531d3b740a819e3d/node_modules/@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "daba362e", "needsInterop": false}, "class-variance-authority": {"src": "../../.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "136d787b", "needsInterop": false}, "clsx": {"src": "../../.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "f913b195", "needsInterop": false}, "lucide-react": {"src": "../../.pnpm/lucide-react@0.364.0_react@18.3.1/node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "9a98d592", "needsInterop": false}, "react-dom/client": {"src": "../../.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "d5e28f3b", "needsInterop": true}, "react-router-dom": {"src": "../../.pnpm/react-router-dom@6.30.1_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "c308a766", "needsInterop": false}, "tailwind-merge": {"src": "../../.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "95c3f0b1", "needsInterop": false}}, "chunks": {"chunk-DEX2RCYB": {"file": "chunk-DEX2RCYB.js"}, "chunk-6IP4XDIZ": {"file": "chunk-6IP4XDIZ.js"}, "chunk-GBGQJTXO": {"file": "chunk-GBGQJTXO.js"}, "chunk-OF2UUMK3": {"file": "chunk-OF2UUMK3.js"}, "chunk-SUIRU742": {"file": "chunk-SUIRU742.js"}, "chunk-24KZPFHU": {"file": "chunk-24KZPFHU.js"}, "chunk-NLPANMJC": {"file": "chunk-NLPANMJC.js"}, "chunk-ZSCOC3NL": {"file": "chunk-ZSCOC3NL.js"}, "chunk-NNZQKBPS": {"file": "chunk-NNZQKBPS.js"}, "chunk-2OBKLRK6": {"file": "chunk-2OBKLRK6.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}