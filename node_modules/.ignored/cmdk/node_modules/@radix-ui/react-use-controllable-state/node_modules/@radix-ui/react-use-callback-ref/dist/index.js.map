{"mappings": ";;;;;;;ACAA;AAEA;;;GAGA,CACA,SAASA,yCAAT,CAA2DE,QAA3D,EAAuF;IACrF,MAAMC,WAAW,GAAGF,mBAAA,CAAaC,QAAb,CAApB,AAAA;IAEAD,sBAAA,CAAgB,IAAM;QACpBE,WAAW,CAACG,OAAZ,GAAsBJ,QAAtB,CAAAC;KADF,CAAA,CAHqF,CAOrF,iDAFC;IAGD,OAAOF,oBAAA,CAAc,IAAO,CAAIO,GAAAA,IAAJ,GAA5B;YAA4B,IAAA,oBAAA,AAAA;YAAA,OAAA,AAAA,CAAA,oBAAA,GAAaL,WAAW,CAACG,OAAzB,CAAA,KAAA,IAAA,IAAA,oBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAa,oBAAA,CAAA,IAAA,CAAAH,WAAW,KAAcK,IAAd,CAAxB,CAAA;SAArB;IAAA,EAAwE,EAAxE,CAAP,CAA4B;CAC7B;;ADfD", "sources": ["packages/react/use-callback-ref/src/index.ts", "packages/react/use-callback-ref/src/useCallbackRef.tsx"], "sourcesContent": ["export { useCallbackRef } from './useCallbackRef';\n", "import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n"], "names": ["useCallbackRef", "React", "callback", "callback<PERSON><PERSON>", "useRef", "useEffect", "current", "useMemo", "args"], "version": 3, "file": "index.js.map"}