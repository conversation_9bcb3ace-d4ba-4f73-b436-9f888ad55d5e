{"version": 3, "file": "joi.umd.js", "sources": ["../src/joi.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport type { ValidationError } from 'joi';\nimport { FieldError, appendErrors } from 'react-hook-form';\nimport { Resolver } from './types';\n\nconst parseErrorSchema = (\n  error: ValidationError,\n  validateAllFieldCriteria: boolean,\n) =>\n  error.details.length\n    ? error.details.reduce<Record<string, FieldError>>((previous, error) => {\n        const _path = error.path.join('.');\n\n        if (!previous[_path]) {\n          previous[_path] = { message: error.message, type: error.type };\n        }\n\n        if (validateAllFieldCriteria) {\n          const types = previous[_path].types;\n          const messages = types && types[error.type!];\n\n          previous[_path] = appendErrors(\n            _path,\n            validateAllFieldCriteria,\n            previous,\n            error.type,\n            messages\n              ? ([] as string[]).concat(messages as string[], error.message)\n              : error.message,\n          ) as FieldError;\n        }\n\n        return previous;\n      }, {})\n    : {};\n\nexport const joiResolver: Resolver =\n  (\n    schema,\n    schemaOptions = {\n      abortEarly: false,\n    },\n    resolverOptions = {},\n  ) =>\n  async (values, context, options) => {\n    const _schemaOptions = Object.assign({}, schemaOptions, {\n      context,\n    });\n\n    let result: Record<string, any> = {};\n    if (resolverOptions.mode === 'sync') {\n      result = schema.validate(values, _schemaOptions);\n    } else {\n      try {\n        result.value = await schema.validateAsync(values, _schemaOptions);\n      } catch (e) {\n        result.error = e;\n      }\n    }\n\n    if (result.error) {\n      return {\n        values: {},\n        errors: toNestErrors(\n          parseErrorSchema(\n            result.error,\n            !options.shouldUseNativeValidation &&\n              options.criteriaMode === 'all',\n          ),\n          options,\n        ),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      errors: {},\n      values: result.value,\n    };\n  };\n"], "names": ["schema", "schemaOptions", "resolverOptions", "abort<PERSON><PERSON><PERSON>", "values", "context", "options", "_temp3", "result", "error", "errors", "toNestErrors", "validateAllFieldCriteria", "shouldUseNativeValidation", "criteriaMode", "details", "length", "reduce", "previous", "_path", "path", "join", "message", "type", "types", "messages", "appendErrors", "concat", "validateFieldsNatively", "value", "_schemaOptions", "Object", "assign", "_temp2", "mode", "validate", "_temp", "_catch", "Promise", "resolve", "validateAsync", "then", "_schema$validateAsync", "e", "reject"], "mappings": "sYAqCE,SACEA,EACAC,EAGAC,GAAoB,YAHpBD,IAAAA,IAAAA,EAAgB,CACdE,YAAY,SAEdD,IAAAA,IAAAA,EAAkB,CAAE,GAAA,SAEfE,EAAQC,EAASC,GAAW,IAAA,IAAAC,EAAAA,WAgBjC,OAAIC,EAAOC,MACF,CACLL,OAAQ,CAAA,EACRM,OAAQC,EAAAA,cAzDdF,EA2DUD,EAAOC,MA1DjBG,GA2DWN,EAAQO,2BACkB,QAAzBP,EAAQQ,aA1DpBL,EAAMM,QAAQC,OACVP,EAAMM,QAAQE,OAAmC,SAACC,EAAUT,GAC1D,IAAMU,EAAQV,EAAMW,KAAKC,KAAK,KAM9B,GAJKH,EAASC,KACZD,EAASC,GAAS,CAAEG,QAASb,EAAMa,QAASC,KAAMd,EAAMc,OAGtDX,EAA0B,CAC5B,IAAMY,EAAQN,EAASC,GAAOK,MACxBC,EAAWD,GAASA,EAAMf,EAAMc,MAEtCL,EAASC,GAASO,EAAAA,aAChBP,EACAP,EACAM,EACAT,EAAMc,KACNE,EACK,GAAgBE,OAAOF,EAAsBhB,EAAMa,SACpDb,EAAMa,QAEd,CAEA,OAAOJ,CACT,EAAG,CAAA,GACH,CAAE,GAmCEZ,KAKNA,EAAQO,2BAA6Be,EAAsBA,uBAAC,GAAItB,GAEzD,CACLI,OAAQ,CAAA,EACRN,OAAQI,EAAOqB,QAzEI,IACvBpB,EACAG,CAwEI,EAlCIkB,EAAiBC,OAAOC,OAAO,CAAA,EAAI/B,EAAe,CACtDI,QAAAA,IAGEG,EAA8B,CAAA,EAAGyB,gBACR,SAAzB/B,EAAgBgC,KAClB1B,EAASR,EAAOmC,SAAS/B,EAAQ0B,OAAgBM,CAAAA,IAAAA,uFAAAC,YAE7CC,OAAAA,QAAAC,QACmBvC,EAAOwC,cAAcpC,EAAQ0B,IAAeW,KAAA,SAAAC,GAAjElC,EAAOqB,MAAKa,CAAsD,EACpE,EAASC,SAAAA,GACPnC,EAAOC,MAAQkC,CACjB,GAACP,GAAAA,GAAAA,EAAAK,YAAAL,EAAAK,KAAA,aAAA,CAAA,IAAA,OAAAH,QAAAC,QAAAN,GAAAA,EAAAQ,KAAAR,EAAAQ,KAAAlC,GAAAA,IAuBL,CAAC,MAAAoC,UAAAL,QAAAM,OAAAD,EAAA,CAAA,CAAA"}