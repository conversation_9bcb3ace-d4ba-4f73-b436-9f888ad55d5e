nohup: ignoring input

> knowledge-base-backend@1.0.0 dev
> nodemon --no-stdin src/server.js

[33m[nodemon] 3.1.10[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: js,mjs,cjs,json[39m
[32m[nodemon] starting `node src/server.js`[39m
🚀 服务器启动成功
📍 服务地址: http://9.135.136.221:5000
🔗 健康检查: http://9.135.136.221:5000/api/health
📂 环境变量: development
ValidationError: The 'X-Forwarded-For' header is set but the Express 'trust proxy' setting is false (default). This could indicate a misconfiguration which would prevent express-rate-limit from accurately identifying users. See https://express-rate-limit.github.io/ERR_ERL_UNEXPECTED_X_FORWARDED_FOR/ for more information.
    at Object.xForwardedForHeader (/data1/npm/knowledge-base-platform/backend/node_modules/express-rate-limit/dist/index.cjs:185:13)
    at Object.wrappedValidations.<computed> [as xForwardedForHeader] (/data1/npm/knowledge-base-platform/backend/node_modules/express-rate-limit/dist/index.cjs:397:22)
    at Object.keyGenerator (/data1/npm/knowledge-base-platform/backend/node_modules/express-rate-limit/dist/index.cjs:658:20)
    at /data1/npm/knowledge-base-platform/backend/node_modules/express-rate-limit/dist/index.cjs:710:32
    at processTicksAndRejections (node:internal/process/task_queues:96:5)
    at async /data1/npm/knowledge-base-platform/backend/node_modules/express-rate-limit/dist/index.cjs:691:5 {
  code: 'ERR_ERL_UNEXPECTED_X_FORWARDED_FOR',
  help: 'https://express-rate-limit.github.io/ERR_ERL_UNEXPECTED_X_FORWARDED_FOR/'
}
::ffff:************ - - [26/Jun/2025:05:59:56 +0000] "GET /api/health HTTP/1.1" 200 104 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
::ffff:************ - - [26/Jun/2025:06:00:02 +0000] "GET / HTTP/1.1" 200 209 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
::ffff:************ - - [26/Jun/2025:06:00:08 +0000] "GET /api HTTP/1.1" 404 56 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
::ffff:************ - - [26/Jun/2025:06:00:14 +0000] "GET / HTTP/1.1" 304 - "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
::ffff:************ - - [26/Jun/2025:06:00:26 +0000] "GET /api/knowledge-base HTTP/1.1" 404 71 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
node:events:491
      throw er; // Unhandled 'error' event
      ^

Error: EBADF: bad file descriptor, read
Emitted 'error' event on ReadStream instance at:
    at emitErrorNT (node:internal/streams/destroy:157:8)
    at errorOrDestroy (node:internal/streams/destroy:220:7)
    at node:internal/fs/streams:262:9
    at FSReqCallback.wrapper [as oncomplete] (node:fs:671:5) {
  errno: -9,
  code: 'EBADF',
  syscall: 'read'
}
