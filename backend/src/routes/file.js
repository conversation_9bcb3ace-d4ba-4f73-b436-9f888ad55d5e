const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();

// 通用文件上传配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/general');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 20 * 1024 * 1024, // 20MB
    files: 5 // 最多5个文件
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.csv', '.txt', '.json', '.xml'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error(`不支持的文件格式: ${ext}，只支持 ${allowedTypes.join(', ')}`));
    }
  }
});

// 单文件上传
router.post('/upload/single', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    const fileInfo = {
      originalName: req.file.originalname,
      filename: req.file.filename,
      size: req.file.size,
      mimetype: req.file.mimetype,
      path: req.file.path,
      uploadTime: new Date().toISOString()
    };

    console.log('文件上传成功:', fileInfo);

    res.json({
      success: true,
      message: '文件上传成功',
      data: fileInfo
    });
  } catch (error) {
    console.error('文件上传失败:', error.message);
    res.status(500).json({
      success: false,
      message: `上传失败: ${error.message}`
    });
  }
});

// 多文件上传
router.post('/upload/multiple', upload.array('files', 5), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    const filesInfo = req.files.map(file => ({
      originalName: file.originalname,
      filename: file.filename,
      size: file.size,
      mimetype: file.mimetype,
      path: file.path,
      uploadTime: new Date().toISOString()
    }));

    console.log('多文件上传成功:', filesInfo);

    res.json({
      success: true,
      message: `成功上传 ${req.files.length} 个文件`,
      data: filesInfo
    });
  } catch (error) {
    console.error('多文件上传失败:', error.message);
    res.status(500).json({
      success: false,
      message: `上传失败: ${error.message}`
    });
  }
});

// 文件下载
router.get('/download/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, '../../uploads/general', filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    const stat = fs.statSync(filePath);
    res.setHeader('Content-Length', stat.size);
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename=${filename}`);

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('文件下载失败:', error.message);
    res.status(500).json({
      success: false,
      message: `下载失败: ${error.message}`
    });
  }
});

// 文件信息查询
router.get('/info/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, '../../uploads/general', filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    const stat = fs.statSync(filePath);
    const fileInfo = {
      filename: filename,
      size: stat.size,
      created: stat.birthtime,
      modified: stat.mtime,
      isFile: stat.isFile(),
      extension: path.extname(filename).toLowerCase()
    };

    res.json({
      success: true,
      data: fileInfo
    });
  } catch (error) {
    console.error('获取文件信息失败:', error.message);
    res.status(500).json({
      success: false,
      message: `获取文件信息失败: ${error.message}`
    });
  }
});

// 文件删除
router.delete('/:filename', (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, '../../uploads/general', filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    fs.unlinkSync(filePath);
    console.log('文件删除成功:', filename);

    res.json({
      success: true,
      message: `文件 ${filename} 删除成功`
    });
  } catch (error) {
    console.error('文件删除失败:', error.message);
    res.status(500).json({
      success: false,
      message: `删除失败: ${error.message}`
    });
  }
});

// 列出上传的文件
router.get('/list', (req, res) => {
  try {
    const uploadDir = path.join(__dirname, '../../uploads/general');
    
    if (!fs.existsSync(uploadDir)) {
      return res.json({
        success: true,
        data: []
      });
    }

    const files = fs.readdirSync(uploadDir).map(filename => {
      const filePath = path.join(uploadDir, filename);
      const stat = fs.statSync(filePath);
      
      return {
        filename: filename,
        size: stat.size,
        created: stat.birthtime,
        modified: stat.mtime,
        extension: path.extname(filename).toLowerCase(),
        isFile: stat.isFile()
      };
    }).filter(file => file.isFile);

    res.json({
      success: true,
      data: files,
      total: files.length
    });
  } catch (error) {
    console.error('获取文件列表失败:', error.message);
    res.status(500).json({
      success: false,
      message: `获取文件列表失败: ${error.message}`
    });
  }
});

// 清理临时文件（定期清理超过24小时的文件）
router.post('/cleanup', (req, res) => {
  try {
    const uploadDir = path.join(__dirname, '../../uploads/general');
    
    if (!fs.existsSync(uploadDir)) {
      return res.json({
        success: true,
        message: '没有文件需要清理',
        data: { deletedCount: 0 }
      });
    }

    const files = fs.readdirSync(uploadDir);
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    let deletedCount = 0;

    files.forEach(filename => {
      const filePath = path.join(uploadDir, filename);
      const stat = fs.statSync(filePath);
      
      if (stat.isFile() && (now - stat.mtime.getTime()) > maxAge) {
        fs.unlinkSync(filePath);
        deletedCount++;
        console.log('清理过期文件:', filename);
      }
    });

    res.json({
      success: true,
      message: `清理完成，删除了 ${deletedCount} 个过期文件`,
      data: { deletedCount }
    });
  } catch (error) {
    console.error('清理文件失败:', error.message);
    res.status(500).json({
      success: false,
      message: `清理失败: ${error.message}`
    });
  }
});

// 文件格式转换
router.post('/convert', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要转换的文件'
      });
    }

    const { targetFormat } = req.body;
    
    if (!targetFormat || !['csv', 'txt', 'json'].includes(targetFormat)) {
      return res.status(400).json({
        success: false,
        message: '不支持的目标格式，支持: csv, txt, json'
      });
    }

    const content = fs.readFileSync(req.file.path, 'utf8');
    const originalExt = path.extname(req.file.originalname).toLowerCase();
    
    let convertedContent = content;
    let contentType = 'text/plain';
    let newFilename = path.basename(req.file.originalname, originalExt) + '.' + targetFormat;

    // 简单的格式转换逻辑
    switch (targetFormat) {
      case 'csv':
        contentType = 'text/csv';
        break;
      case 'json':
        contentType = 'application/json';
        // 尝试转换为JSON格式
        try {
          if (originalExt === '.csv') {
            // 简单的CSV到JSON转换
            const lines = content.split('\n').filter(line => line.trim());
            if (lines.length > 0) {
              const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
              const jsonData = lines.slice(1).map(line => {
                const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
                const obj = {};
                headers.forEach((header, index) => {
                  obj[header] = values[index] || '';
                });
                return obj;
              });
              convertedContent = JSON.stringify(jsonData, null, 2);
            }
          }
        } catch (err) {
          console.log('转换为JSON失败，使用原始内容');
        }
        break;
      default:
        contentType = 'text/plain';
    }

    // 清理原始文件
    fs.unlinkSync(req.file.path);

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename=${newFilename}`);
    res.send(convertedContent);
  } catch (error) {
    console.error('文件转换失败:', error.message);
    
    // 清理文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: `转换失败: ${error.message}`
    });
  }
});

module.exports = router;
