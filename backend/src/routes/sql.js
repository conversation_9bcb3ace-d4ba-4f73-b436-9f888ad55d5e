const express = require('express');
const axios = require('axios');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();

// 文件上传配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/sql');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const decodedFilename = decodeURIComponent(file.originalname);
    cb(null, 'sql-' + uniqueSuffix + path.extname(decodedFilename));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/plain' || path.extname(file.originalname).toLowerCase() === '.txt') {
      cb(null, true);
    } else {
      cb(new Error('只支持TXT文件格式'));
    }
  }
});

// 搜索SQL数据
router.get('/search', async (req, res) => {
  try {
    const { query, page = 1, limit = 20, knowledge_base_id } = req.query;
    
    // 这里应该调用实际的搜索API
    // 暂时返回模拟数据
    const mockData = [
      {
        id: 'sql001',
        name: 'fqs查询',
        description: 'agg函数在having中',
        sql_content: 'select * from fqs_shard_1 group by id having count(*) > 1000;',
        tags: ['聚合函数', 'having子句']
      },
      {
        id: 'sql002',
        name: '复杂聚合查询',
        description: '嵌套查询中的聚合函数',
        sql_content: 'select max(max) from (select max(dt) from fqs_multi_shard_1 group by id,deptno,ts having avg(age) = 10);',
        tags: ['嵌套查询', '聚合函数']
      },
      {
        id: 'sql003',
        name: '窗口函数示例',
        description: '使用窗口函数进行排名',
        sql_content: 'select id, name, row_number() over (partition by dept_id order by salary desc) as rank from employees;',
        tags: ['窗口函数', '排名']
      },
      {
        id: 'sql004',
        name: '日期过滤查询',
        description: '按日期范围过滤数据',
        sql_content: 'select max(avg) from (select avg(age) from fqs_list_1 group by address,dt having max(dt) > \'2025-10-01\');',
        tags: ['日期过滤', '聚合函数']
      }
    ];

    // 根据查询参数过滤数据
    let filteredData = mockData;
    if (query) {
      filteredData = mockData.filter(item => 
        item.name.includes(query) || 
        item.description.includes(query) ||
        item.sql_content.includes(query) ||
        (item.tags && item.tags.some(tag => tag.includes(query)))
      );
    }

    res.json({
      success: true,
      data: filteredData,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: filteredData.length
      }
    });
  } catch (error) {
    console.error('搜索SQL数据失败:', error.message);
    res.status(500).json({
      success: false,
      error: `搜索失败: ${error.message}`
    });
  }
});

// 上传SQL数据（从Python代码转换）
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的TXT文件'
      });
    }

    const ragId = req.body.rag_id;
    if (!ragId) {
      return res.status(400).json({
        success: false,
        message: '缺少知识库ID'
      });
    }

    const config = {
      apiKey: process.env.API_KEY || req.headers['x-api-key'],
      apiIp: process.env.API_IP || req.headers['x-api-ip'] || 'localhost:8000',
    };

    if (!config.apiKey) {
      return res.status(400).json({
        success: false,
        message: '缺少API密钥'
      });
    }

    const headers = {
      'Authorization': `Bearer ${config.apiKey}`,
    };

    const metadata = {
      indexing_technique: "high_quality",
      doc_form: "hierarchical_model",
      doc_language: "Chinese",
      process_rule: {
        mode: "hierarchical",
        rules: {
          parent_mode: "paragraph",
          pre_processing_rules: [
            { id: "remove_extra_spaces", enabled: true },
            { id: "remove_urls_emails", enabled: true }
          ],
          segmentation: {
            separator: "\n\n",
            max_tokens: 4000
          },
          subchunk_segmentation: {
            separator: "\n:ident:",
            max_tokens: 4000
          }
        }
      }
    };

    const url = `http://${config.apiIp}/v1/datasets/${ragId}/document/create-by-file`;
    
    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('data', JSON.stringify(metadata));
    formData.append('file', fs.createReadStream(req.file.path), {
      filename: decodeURIComponent(req.file.originalname),
      contentType: 'text/plain'
    });

    const response = await axios.post(url, formData, {
      headers: {
        ...headers,
        ...formData.getHeaders()
      },
      timeout: 30000
    });

    // 清理上传的临时文件
    fs.unlinkSync(req.file.path);

    if (response.status === 200 || response.status === 201) {
      res.json({
        success: true,
        file_id: response.data.id,
        message: 'SQL数据上传成功',
        data: response.data
      });
    } else {
      res.status(response.status).json({
        success: false,
        message: `上传失败: HTTP ${response.status}`
      });
    }
  } catch (error) {
    console.error('上传SQL数据失败:', error.message);
    
    // 清理上传的临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    if (error.response) {
      res.status(error.response.status).json({
        success: false,
        message: `外部API错误: ${error.response.data?.message || error.message}`
      });
    } else {
      res.status(500).json({
        success: false,
        message: `上传错误: ${error.message}`
      });
    }
  }
});

// 删除SQL数据
router.delete('/:name', async (req, res) => {
  try {
    const sqlName = req.params.name;
    const { knowledge_base_id } = req.query;

    if (!knowledge_base_id) {
      return res.status(400).json({
        success: false,
        message: '缺少知识库ID'
      });
    }

    // 这里应该调用实际的删除API
    // 暂时模拟删除操作
    console.log(`删除SQL: ${sqlName} from 知识库: ${knowledge_base_id}`);

    res.json({
      success: true,
      message: `SQL "${sqlName}" 删除成功`
    });
  } catch (error) {
    console.error('删除SQL数据失败:', error.message);
    res.status(500).json({
      success: false,
      message: `删除失败: ${error.message}`
    });
  }
});

// 获取SQL详情
router.get('/:id', async (req, res) => {
  try {
    const sqlId = req.params.id;
    
    // 这里应该调用实际的获取API
    // 暂时返回模拟数据
    const mockSql = {
      id: sqlId,
      name: '示例SQL',
      description: '这是一个示例SQL查询',
      sql_content: 'SELECT * FROM example_table WHERE id > 1000;',
      tags: ['示例', '查询']
    };

    res.json({
      success: true,
      data: mockSql
    });
  } catch (error) {
    console.error('获取SQL详情失败:', error.message);
    res.status(500).json({
      success: false,
      error: `获取SQL详情失败: ${error.message}`
    });
  }
});

// 文本转TXT端点
router.post('/convert/text-to-txt', (req, res) => {
  try {
    const { content } = req.body;
    
    if (!content) {
      return res.status(400).json({
        success: false,
        message: '内容不能为空'
      });
    }

    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Content-Disposition', 'attachment; filename=sql_data.txt');
    res.send(content);
  } catch (error) {
    console.error('文本转TXT失败:', error.message);
    res.status(500).json({
      success: false,
      message: `转换失败: ${error.message}`
    });
  }
});

// 验证SQL格式
router.post('/validate', (req, res) => {
  try {
    const { content } = req.body;
    
    if (!content) {
      return res.status(400).json({
        success: false,
        message: '内容不能为空'
      });
    }

    // 简单的格式验证
    const sections = content.split('\n:ident:\n');
    const validSections = [];
    
    for (let i = 0; i < sections.length; i++) {
      const section = sections[i].trim();
      if (section) {
        const lines = section.split('\n');
        if (lines.length > 0) {
          const firstLine = lines[0].trim();
          if (firstLine.includes(',')) {
            const [name, description] = firstLine.split(',', 2);
            validSections.push({
              name: name.trim(),
              description: description.trim(),
              content: lines.slice(1).join('\n').trim()
            });
          }
        }
      }
    }

    res.json({
      success: true,
      message: '格式验证完成',
      data: {
        totalSections: sections.length,
        validSections: validSections.length,
        sections: validSections
      }
    });
  } catch (error) {
    console.error('SQL格式验证失败:', error.message);
    res.status(500).json({
      success: false,
      message: `验证失败: ${error.message}`
    });
  }
});

module.exports = router;
