const express = require('express');
const axios = require('axios');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();

// 文件上传配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/usecase');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'usecase-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || path.extname(file.originalname).toLowerCase() === '.csv') {
      cb(null, true);
    } else {
      cb(new Error('只支持CSV文件格式'));
    }
  }
});

// 搜索用例数据
router.get('/search', async (req, res) => {
  try {
    const { query, page = 1, limit = 20, knowledge_base_id } = req.query;
    
    // 这里应该调用实际的搜索API
    // 暂时返回模拟数据
    const mockData = [
      {
        id: 'uc001',
        name: '用户登录验证',
        description: '用户登录系统的验证流程',
        type: '身份验证',
        db_type: 'MySQL',
        content: ['验证用户名和密码', '检查账户状态', '生成会话令牌']
      },
      {
        id: 'uc002',
        name: '数据导入处理',
        description: '批量数据导入的处理流程',
        type: '数据处理',
        db_type: 'PostgreSQL',
        content: ['数据格式验证', '重复数据检测', '事务处理']
      },
      {
        id: 'uc003',
        name: '权限管理系统',
        description: '用户权限分配和管理',
        type: '权限控制',
        db_type: 'MySQL',
        content: ['角色定义', '权限分配', '访问控制验证']
      }
    ];

    // 根据查询参数过滤数据
    let filteredData = mockData;
    if (query) {
      filteredData = mockData.filter(item => 
        item.name.includes(query) || 
        item.description.includes(query) ||
        item.type.includes(query) ||
        item.db_type.includes(query)
      );
    }

    res.json({
      success: true,
      data: filteredData,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: filteredData.length
      }
    });
  } catch (error) {
    console.error('搜索用例数据失败:', error.message);
    res.status(500).json({
      success: false,
      error: `搜索失败: ${error.message}`
    });
  }
});

// 上传用例数据（从Python代码转换）
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的CSV文件'
      });
    }

    const ragId = req.body.rag_id;
    if (!ragId) {
      return res.status(400).json({
        success: false,
        message: '缺少知识库ID'
      });
    }

    const config = {
      apiKey: process.env.API_KEY || req.headers['x-api-key'],
      apiIp: process.env.API_IP || req.headers['x-api-ip'] || 'localhost:8000',
    };

    if (!config.apiKey) {
      return res.status(400).json({
        success: false,
        message: '缺少API密钥'
      });
    }

    const headers = {
      'Authorization': `Bearer ${config.apiKey}`,
    };

    const metadata = {
      indexing_technique: "high_quality",
      doc_form: "text_model",
      doc_language: "Chinese",
      process_rule: {
        mode: "custom",
        rules: {
          pre_processing_rules: [
            { id: "remove_extra_spaces", enabled: true },
            { id: "remove_urls_emails", enabled: true }
          ],
          segmentation: {
            separator: "\n\n",
            max_tokens: 4000,
            chunk_overlap: 200
          }
        }
      }
    };

    const url = `http://${config.apiIp}/v1/datasets/${ragId}/document/create-by-file`;
    
    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('data', JSON.stringify(metadata));
    formData.append('file', fs.createReadStream(req.file.path), {
      filename: req.file.originalname,
      contentType: 'text/csv'
    });

    const response = await axios.post(url, formData, {
      headers: {
        ...headers,
        ...formData.getHeaders()
      },
      timeout: 30000
    });

    // 清理上传的临时文件
    fs.unlinkSync(req.file.path);

    if (response.status === 200 || response.status === 201) {
      res.json({
        success: true,
        file_id: response.data.id,
        message: '用例数据上传成功',
        data: response.data
      });
    } else {
      res.status(response.status).json({
        success: false,
        message: `上传失败: HTTP ${response.status}`
      });
    }
  } catch (error) {
    console.error('上传用例数据失败:', error.message);
    
    // 清理上传的临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    if (error.response) {
      res.status(error.response.status).json({
        success: false,
        message: `外部API错误: ${error.response.data?.message || error.message}`
      });
    } else {
      res.status(500).json({
        success: false,
        message: `上传错误: ${error.message}`
      });
    }
  }
});

// 删除用例数据
router.delete('/:name', async (req, res) => {
  try {
    const useCaseName = req.params.name;
    const { knowledge_base_id } = req.query;

    if (!knowledge_base_id) {
      return res.status(400).json({
        success: false,
        message: '缺少知识库ID'
      });
    }

    // 这里应该调用实际的删除API
    // 暂时模拟删除操作
    console.log(`删除用例: ${useCaseName} from 知识库: ${knowledge_base_id}`);

    res.json({
      success: true,
      message: `用例 "${useCaseName}" 删除成功`
    });
  } catch (error) {
    console.error('删除用例数据失败:', error.message);
    res.status(500).json({
      success: false,
      message: `删除失败: ${error.message}`
    });
  }
});

// 获取用例详情
router.get('/:id', async (req, res) => {
  try {
    const useCaseId = req.params.id;
    
    // 这里应该调用实际的获取API
    // 暂时返回模拟数据
    const mockUseCase = {
      id: useCaseId,
      name: '示例用例',
      description: '这是一个示例用例',
      type: '演示',
      db_type: 'MySQL',
      content: ['步骤1', '步骤2', '步骤3']
    };

    res.json({
      success: true,
      data: mockUseCase
    });
  } catch (error) {
    console.error('获取用例详情失败:', error.message);
    res.status(500).json({
      success: false,
      error: `获取用例详情失败: ${error.message}`
    });
  }
});

// JSON转CSV工具函数
function jsonToCsv(jsonData) {
  if (jsonData.length === 0) return '';

  const headers = ['id', 'name', 'description', 'type', 'db_type', 'content'];
  
  const rows = jsonData.map(item => [
    item.id,
    item.name,
    item.description,
    item.type,
    item.db_type,
    Array.isArray(item.content) ? item.content.join(';') : item.content
  ]);

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n');

  return csvContent;
}

// JSON转CSV端点
router.post('/convert/json-to-csv', (req, res) => {
  try {
    const { data } = req.body;
    
    if (!Array.isArray(data)) {
      return res.status(400).json({
        success: false,
        message: '数据必须是数组格式'
      });
    }

    const csvContent = jsonToCsv(data);
    
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=usecases.csv');
    res.send(csvContent);
  } catch (error) {
    console.error('JSON转CSV失败:', error.message);
    res.status(500).json({
      success: false,
      message: `转换失败: ${error.message}`
    });
  }
});

module.exports = router;
