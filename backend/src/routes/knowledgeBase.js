const express = require('express');
const axios = require('axios');
const router = express.Router();

// 获取知识库ID（从Python代码转换）
async function getKnowledgeBaseId(ragName, config) {
  const headers = {
    'Authorization': `Bearer ${config.apiKey}`,
    'Content-Type': 'application/json',
  };

  const url = `http://${config.apiIp}/v1/datasets`;
  const params = {
    page: 1,
    limit: 20
  };

  try {
    const response = await axios.get(url, {
      headers,
      params,
      timeout: 10000
    });

    if (response.status === 200) {
      const data = response.data.data;
      
      for (const item of data) {
        if (item.name === ragName) {
          console.log(`RAG ID for ${ragName} is ${item.id}`);
          return item.id;
        }
      }
    } else {
      console.error(`Request failed with status code ${response.status}`);
    }
  } catch (error) {
    console.error('Error fetching knowledge base ID:', error.message);
  }

  return null;
}

// 获取知识库列表
router.get('/list', async (req, res) => {
  try {
    const config = {
      apiKey: process.env.API_KEY || req.headers['x-api-key'],
      apiIp: process.env.API_IP || req.headers['x-api-ip'] || 'localhost:8000',
    };

    if (!config.apiKey) {
      return res.status(400).json({
        success: false,
        error: '缺少API密钥'
      });
    }

    const headers = {
      'Authorization': `Bearer ${config.apiKey}`,
      'Content-Type': 'application/json',
    };

    const url = `http://${config.apiIp}/v1/datasets`;
    const params = {
      page: 1,
      limit: 50
    };

    const response = await axios.get(url, {
      headers,
      params,
      timeout: 15000
    });

    if (response.status === 200) {
      const knowledgeBases = response.data.data.map(item => ({
        id: item.id,
        name: item.name,
        description: item.description,
        type: (item.name.includes('SQL') || item.name.includes('sql') || item.name.includes('DDL') || item.name.includes('语法')) ? 'sql' : 'usecase',
        created_at: item.created_at,
        updated_at: item.updated_at,
      }));

      res.json({
        success: true,
        data: knowledgeBases,
      });
    } else {
      res.status(response.status).json({
        success: false,
        error: `外部API请求失败，状态码: ${response.status}`,
      });
    }
  } catch (error) {
    console.error('获取知识库列表失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      res.status(503).json({
        success: false,
        error: '无法连接到外部API服务'
      });
    } else if (error.code === 'ENOTFOUND') {
      res.status(503).json({
        success: false,
        error: 'API服务地址无效'
      });
    } else {
      res.status(500).json({
        success: false,
        error: `网络错误: ${error.message}`
      });
    }
  }
});

// 根据名称获取知识库ID
router.get('/id/:name', async (req, res) => {
  try {
    const ragName = req.params.name;
    const config = {
      apiKey: process.env.API_KEY || req.headers['x-api-key'],
      apiIp: process.env.API_IP || req.headers['x-api-ip'] || 'localhost:8000',
    };

    if (!config.apiKey) {
      return res.status(400).json({
        success: false,
        error: '缺少API密钥'
      });
    }

    const ragId = await getKnowledgeBaseId(ragName, config);
    
    if (ragId) {
      res.json({
        success: true,
        data: {
          name: ragName,
          id: ragId
        }
      });
    } else {
      res.status(404).json({
        success: false,
        error: `未找到名称为 "${ragName}" 的知识库`
      });
    }
  } catch (error) {
    console.error('获取知识库ID失败:', error.message);
    res.status(500).json({
      success: false,
      error: `获取知识库ID失败: ${error.message}`
    });
  }
});

// 获取知识库详情
router.get('/:id', async (req, res) => {
  try {
    const knowledgeBaseId = req.params.id;
    const config = {
      apiKey: process.env.API_KEY || req.headers['x-api-key'],
      apiIp: process.env.API_IP || req.headers['x-api-ip'] || 'localhost:8000',
    };

    if (!config.apiKey) {
      return res.status(400).json({
        success: false,
        error: '缺少API密钥'
      });
    }

    const headers = {
      'Authorization': `Bearer ${config.apiKey}`,
      'Content-Type': 'application/json',
    };

    const url = `http://${config.apiIp}/v1/datasets/${knowledgeBaseId}`;

    const response = await axios.get(url, {
      headers,
      timeout: 10000
    });

    if (response.status === 200) {
      res.json({
        success: true,
        data: response.data
      });
    } else {
      res.status(response.status).json({
        success: false,
        error: `获取知识库详情失败，状态码: ${response.status}`
      });
    }
  } catch (error) {
    console.error('获取知识库详情失败:', error.message);
    res.status(500).json({
      success: false,
      error: `获取知识库详情失败: ${error.message}`
    });
  }
});

// 新增：获取知识库文档列表接口
router.get('/:id/documents', async (req, res) => {
  try {
    const knowledgeBaseId = req.params.id;
    const { page = 1, limit = 20 } = req.query;
    
    const config = {
      apiKey: process.env.API_KEY || req.headers['x-api-key'],
      apiIp: process.env.API_IP || req.headers['x-api-ip'] || 'localhost:8000',
    };

    if (!config.apiKey) {
      return res.status(400).json({
        success: false,
        error: '缺少API密钥'
      });
    }

    const headers = {
      'Authorization': `Bearer ${config.apiKey}`,
      'Content-Type': 'application/json',
    };

    const url = `http://${config.apiIp}/v1/datasets/${knowledgeBaseId}/documents`;
    
    const response = await axios.get(url, {
      headers,
      params: {
        page: parseInt(page),
        limit: parseInt(limit)
      },
      timeout: 10000
    });

    if (response.status === 200) {
      // 格式化返回数据
      const documents = response.data.data.map(doc => ({
        id: doc.id,
        name: doc.name || '未命名文档',
        type: doc.data_source_type,
        status: doc.indexing_status,
        created_at: doc.created_at,
        updated_at: doc.updated_at,
        word_count: doc.word_count || 0,
        character_count: doc.character_count || 0,
        is_archived: doc.archived,
        document_type: doc.doc_type
      }));

      res.json({
        success: true,
        data: documents,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: response.data.total
        }
      });
    } else {
      res.status(response.status).json({
        success: false,
        error: `获取文档列表失败，状态码: ${response.status}`
      });
    }
  } catch (error) {
    console.error('获取知识库文档列表失败:', error.message);
    
    let status = 500;
    let errorMessage = `获取文档列表失败: ${error.message}`;
    
    if (error.response) {
      status = error.response.status;
      errorMessage = `外部API错误: ${error.response.data?.message || error.message}`;
    } else if (error.code === 'ECONNREFUSED') {
      status = 503;
      errorMessage = '无法连接到知识库服务';
    }
    
    res.status(status).json({
      success: false,
      error: errorMessage
    });
  }
});

module.exports = router;
